import mongoose from 'mongoose';
const { Schema } = mongoose;

const gallerySchema = new Schema({
    image: { type: Array, required: true,},
    title: { type: String, required: true, default: '' },
    author: { type: String,required: true, default: '' },
    size: { type: String, default: '' },
    availability: { type: String, default: 'Available' },
    price: { type: String,required: true, default: '' },
}, { timestamps: true });

// Use existing model if it exists, otherwise create a new one
export const Gallery = mongoose.models.Gallery || mongoose.model('Gallery', gallerySchema); 