'use client'
import { Html } from '@react-three/drei'
import React, { useState, useEffect, useRef, useMemo } from 'react'
// import { useRouter } from 'next/navigation'
import Link from 'next/link'
import ImageScalerComponent from '../ImageScalerComponent'
import { settings } from '@/lib/settings'
import { useControls } from 'leva'
import { useIntersect } from '@react-three/drei'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'

function IconGuides({ item, icon, experienceState, disptachExperience, isContentMarker = false }) {
  const refLink = useRef(null)
  const [onHover, setOnHover] = useState(false)

  const handleClick = () => {
    // console.log('IconGuides:',item)
    item?.markerType==='infoDoc' && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_ITEM_ARTICLE_TOGGLE, payload:item?.id})
    item?.markerType==='infoVideo' && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_VIDOE_GALLERY_TOGGLE})
    item?.markerType==='infoImage' && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_STORE_TOGGLE})
  }
  
  // Handle content markers differently - they don't navigate to other 360s
  if (isContentMarker) {
    return (
      <div
        ref={refLink}
        onMouseEnter={() => setOnHover(!onHover)}
        onMouseLeave={() => setOnHover(!onHover)}
        className="flex cursor-pointer relative w-fit h-fit items-center justify-center"
        onClick={() => {
          // TODO: Handle content marker clicks (open modal, show content, etc.)
          handleClick()
          // console.log('Content marker clicked:', item);
        }}
      >
        {!onHover
          ? <ImageScalerComponent src={icon?.btnIcons?.off} alt="marker icon" />
          : <ImageScalerComponent src={icon?.btnIcons?.ov} alt="marker icon" />
        }
      </div>
    );
  }

  // Navigation markers - link to other 360s
  return (
    <Link ref={refLink} href={`/360s?id=${item?._360Name}`}
      onMouseEnter={() => setOnHover(!onHover)}
      onMouseLeave={() => setOnHover(!onHover)}
      className="flex cursor-pointer relative w-fit h-fit items-center justify-center"
    >
        {!onHover
            ? <ImageScalerComponent src={icon?.btnIcons?.off} alt="marker icon" />
            : <ImageScalerComponent src={icon?.btnIcons?.ov} alt="marker icon" />
        }
    </Link>
  )
}

// Create a simpler component that directly sets position without useFrame
const MarkerPosition = ({ position, children }) => {
  // const refGroup = useRef(null)
  const [markerVisble, setMarkerVisble] = useState(false)
  const refGroup = useIntersect((visible)=>setMarkerVisble(visible)) // `visible` will be true if intersecting
  // Ensure position values are numbers with fallbacks
  const x = typeof position.x === 'number' ? position.x : 0
  const y = typeof position.y === 'number' ? position.y : 0
  const z = typeof position.z === 'number' ? position.z : 0
  
  // console.log('MarkerPosition is marker visble:',markerVisble)

  // Just use the position prop directly on the group
  return (
    <group visible={markerVisble} ref={refGroup} position={[x, y, z]}>
      {children}
    </group>
  )
}

const MarkerIcon = ({ item, set_360Object, disptachExperience, experienceState }) => {
  const [onHover, setOnHover] = useState(false)
  const debounceTimeoutRef = useRef(null)

  // Ensure item is an object
  const safeItem = item || {}

  const options = useMemo(() => {
      return {
        x: {
          value: item.x || 0,
          min: -32,
          max: 32,
          step: 0.0001,
          transient: false // Disable transient updates for better performance
        },
        y: {
          value: item.y || 0,
          min: -32,
          max: 32,
          step: 0.0001,
          transient: false
        },
        z: {
          value: item.z || 0,
          min: -32,
          max: 32,
          step: 0.0001,
          transient: false
        }
      }
    }, [item.x, item.y, item.z])

  const controls = useControls(`Marker: ${safeItem.name || 'unnamed'}`, options)

  useEffect(() => {
    // set_360Object({..._360Object,...controls})
  }, [controls])
    
  // Convert item position to a proper object with numeric values
  // Use parseFloat and handle NaN values with || 0
  const position = {
    x: isNaN(parseFloat(controls.x)) ? 0 : parseFloat(controls.x),
    y: isNaN(parseFloat(controls.y)) ? 0 : parseFloat(controls.y),
    z: isNaN(parseFloat(controls.z)) ? 0 : parseFloat(controls.z)
  }

  // Throttled position updates to reduce message handler load
  useEffect(() => {
    if (!safeItem.name || !set_360Object) return;

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      set_360Object(prev => {
        // Ensure we have a valid markerList before updating
        if (!prev.markerList || !Array.isArray(prev.markerList)) {
          return prev;
        }

        return {
          ...prev,
          markerList: prev.markerList.map(m =>
            m.name === safeItem.name ? {...m, ...position} : m
          )
        };
      });
    }, 100); // 100ms throttle for marker position updates

    // Cleanup on unmount
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [position, safeItem.name, set_360Object])
  

  // Debug output removed to reduce console noise
   if (process.env.NODE_ENV === 'development') {
    // console.log('Current marker list:', markerList)
    // console.log('Current input state:', input)
    // console.log('Current 360 object:', _360Object)
  }

  return (
    <MarkerPosition position={position}>
      <Html center zIndexRange={[1, 10]}>
        <div
          onPointerOver={() => setOnHover(!onHover)}
          onPointerOut={() => setOnHover(!onHover)}
          className='flex w-fit cursor-pointer h-fit items-center justify-center'
          style={{ pointerEvents: 'auto' }}
        >
          {safeItem.markerType === 'landingPage'
            ? <Link href={`/360s?id=livingroom_001`}>{settings.markerList.markerTypeIcons.landingPage.btnIcons.off}</Link>
            : safeItem.markerType === 'guide'
              ? <IconGuides
                  experienceState={experienceState}
                  disptachExperience={disptachExperience}
                  item={item}
                  icon={settings.markerList.markerTypeIcons.guide}
                  isContentMarker={false}
                />
              : safeItem.markerType === 'upstairs'
                ? <IconGuides
                    experienceState={experienceState}
                    disptachExperience={disptachExperience}
                    item={item}
                    icon={settings.markerList.markerTypeIcons.upstairs}
                    isContentMarker={false}
                  />
              : safeItem.markerType === 'downstairs'
                ? <IconGuides
                    experienceState={experienceState}
                    disptachExperience={disptachExperience}
                    item={item}
                    icon={settings.markerList.markerTypeIcons.downstairs}
                    isContentMarker={false}
                  />
              : safeItem.markerType === 'infoVideo'
                ? <IconGuides
                    experienceState={experienceState}
                    disptachExperience={disptachExperience}
                    item={item}
                    icon={settings.markerList.markerTypeIcons.infoVideo}
                    isContentMarker={true}
                  />
              : safeItem.markerType === 'infoDoc'
                ? <IconGuides
                    experienceState={experienceState}
                    disptachExperience={disptachExperience}
                    item={item}
                    icon={settings.markerList.markerTypeIcons.infoDoc}
                    isContentMarker={true}
                  />
              : safeItem.markerType === 'infoImage'
                ? <IconGuides
                    experienceState={experienceState}
                    disptachExperience={disptachExperience}
                    item={item}
                    icon={settings.markerList.markerTypeIcons.infoImage}
                    isContentMarker={true}
                  />
              : null
          }
        </div>
      </Html>
    </MarkerPosition>
  )
}

export default function _360InfoMarkers({ markerList, set_360Object, disptachExperience, experienceState, currentImageId }) {
  // Ensure markerList is an array
  const safeMarkerList = Array.isArray(markerList) ? markerList : []

  // console.log('_360InfoMarkers:',experienceState)

  // Create a stable key that includes currentImageId to force re-render on image change
  const componentKey = useMemo(() => {
    return `markers-${currentImageId || 'no-id'}-${safeMarkerList.length}`;
  }, [currentImageId, safeMarkerList.length]);

  // Debug logging for marker synchronization (remove in production)
  // useEffect(() => {
  //   if (process.env.NODE_ENV === 'development') {
  //     console.log('_360InfoMarkers updated:', {
  //       currentImageId,
  //       markerCount: safeMarkerList.length,
  //       markers: safeMarkerList.map(m => ({ name: m?.name, type: m?.markerType }))
  //     });
  //   }
  // }, [currentImageId, safeMarkerList]);

  return (
    <group key={componentKey}>
      {/* Markers section */}
      {safeMarkerList.map((item, index) => (
        // Use a unique key that includes currentImageId to ensure proper updates when switching images
        <MarkerIcon
          key={`${currentImageId}-marker-${item?.name || 'unnamed'}-${index}-${item?.markerType || 'no-type'}`}
          item={item}
          set_360Object={set_360Object}
          disptachExperience={disptachExperience}
          experienceState={experienceState}
        />
      ))}
    </group>
  )
}
