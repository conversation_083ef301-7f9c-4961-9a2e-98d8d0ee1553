'use client';

export default function PackageAnalytics({ package: pkg }) {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Current Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
          <div className="text-2xl font-bold text-blue-900">
            {pkg.stats?.totalBookings || 0}
          </div>
          <div className="text-sm text-blue-700">Total Bookings</div>
        </div>
        
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
          <div className="text-2xl font-bold text-green-900">
            {formatCurrency(pkg.stats?.totalRevenue || 0)}
          </div>
          <div className="text-sm text-green-700">Total Revenue</div>
        </div>
        
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 text-center">
          <div className="text-2xl font-bold text-purple-900">
            {pkg.stats?.averageRating || 0}⭐
          </div>
          <div className="text-sm text-purple-700">Average Rating</div>
        </div>
        
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 text-center">
          <div className="text-2xl font-bold text-orange-900">
            {pkg.stats?.reviewCount || 0}
          </div>
          <div className="text-sm text-orange-700">Reviews</div>
        </div>
      </div>

      {/* Coming Soon */}
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📊</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Analytics</h3>
        <p className="text-gray-600 mb-4">
          Detailed performance analytics and insights for your package.
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm text-blue-800">
          <strong>Coming Soon:</strong> Booking trends, revenue charts, seasonal performance, 
          conversion rates, and competitive analysis.
        </div>
      </div>
    </div>
  );
}
