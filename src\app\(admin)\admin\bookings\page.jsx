import BookingManagementDashboard from '@/components/bookings/BookingManagementDashboard';

// Force dynamic rendering for this page since it requires authentication
export const dynamic = 'force-dynamic';

export default async function AdminBookingsPage() {
  // No authentication required - page is now public
  // Require manager role or higher
return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="px-4 py-6 sm:px-0">
          <div className="border-b border-gray-200 pb-4">
            <h1 className="text-3xl font-bold leading-tight text-gray-900">
              Booking Management
            </h1>
            <p className="mt-2 text-sm text-gray-600">
              Manage bookings, check-ins, check-outs, and guest communications.
            </p>
          </div>
        </div>

        {/* Booking Management Dashboard */}
        <BookingManagementDashboard />
      </div>
    </div>
  );
}
