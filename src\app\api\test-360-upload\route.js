import { NextResponse } from 'next/server';
import { uploadFileServer } from '@/lib/server-file-upload';

// POST /api/test-360-upload - Test 360° upload workflow with a dummy file
export async function POST(request) {
  try {
    console.log('Testing 360° upload workflow...');
    
    // Create a small test file to simulate upload
    const testContent = 'Test 360 image content';
    const testFile = new File([testContent], 'test-360.jpg', {
      type: 'image/jpeg',
      lastModified: Date.now()
    });
    
    console.log('Created test file:', {
      name: testFile.name,
      size: testFile.size,
      type: testFile.type
    });
    
    // Test the upload process
    const uploadResult = await uploadFileServer(testFile, '360s', 'test-360-upload.jpg');
    
    console.log('Upload result:', uploadResult);
    
    return NextResponse.json({
      success: true,
      message: '360° upload test completed',
      uploadResult,
      analysis: {
        isFirebaseURL: uploadResult.url?.includes('firebasestorage.googleapis.com'),
        isLocalURL: uploadResult.url?.startsWith('/uploads/'),
        storageType: uploadResult.storage,
        urlFormat: uploadResult.url?.substring(0, 50) + '...'
      }
    });
  } catch (error) {
    console.error('360° upload test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack,
      troubleshooting: {
        possibleCauses: [
          'Firebase configuration invalid',
          'Storage bucket permissions issue',
          'Network connectivity problem',
          'File upload handler error'
        ]
      }
    }, { status: 500 });
  }
}

// GET /api/test-360-upload - Show test information
export async function GET(request) {
  return NextResponse.json({
    message: 'Use POST method to test 360° upload workflow',
    endpoint: '/api/test-360-upload',
    method: 'POST',
    description: 'Tests the complete 360° upload workflow including Firebase Storage integration'
  });
}
