'use client';

import { useState } from 'react';

export default function BookingActions({ 
  booking, 
  onBookingUpdate, 
  onBookingDelete, 
  onBookingSelect 
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  const handleAction = async (action, data = {}) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/bookings/${booking._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, ...data }),
      });

      const result = await response.json();
      
      if (result.success) {
        onBookingUpdate(result.data);
        setShowDropdown(false);
      } else {
        alert(result.message || 'Action failed');
      }
    } catch (error) {
      alert('Failed to perform action');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete booking ${booking.bookingNumber}? This action cannot be undone.`)) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/bookings/${booking._id}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      
      if (result.success) {
        onBookingDelete(booking._id);
      } else {
        alert(result.message || 'Failed to delete booking');
      }
    } catch (error) {
      alert('Failed to delete booking');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = (newStatus) => {
    const statusMessages = {
      confirmed: 'Confirm this booking?',
      checked_in: 'Check in this guest?',
      checked_out: 'Check out this guest?',
      cancelled: 'Cancel this booking?',
      no_show: 'Mark as no-show?',
    };

    if (confirm(statusMessages[newStatus] || `Change status to ${newStatus}?`)) {
      handleAction('change_status', { status: newStatus });
    }
  };

  const getAvailableActions = () => {
    const actions = [];
    
    switch (booking.status) {
      case 'pending':
        actions.push(
          { action: 'confirmed', label: 'Confirm Booking', color: 'text-blue-700' },
          { action: 'cancelled', label: 'Cancel Booking', color: 'text-red-700' }
        );
        break;
      case 'confirmed':
        actions.push(
          { action: 'checked_in', label: 'Check In', color: 'text-green-700' },
          { action: 'no_show', label: 'Mark No-Show', color: 'text-purple-700' },
          { action: 'cancelled', label: 'Cancel Booking', color: 'text-red-700' }
        );
        break;
      case 'checked_in':
        actions.push(
          { action: 'checked_out', label: 'Check Out', color: 'text-gray-700' }
        );
        break;
      case 'checked_out':
        // No status changes available for checked out bookings
        break;
      case 'cancelled':
        actions.push(
          { action: 'confirmed', label: 'Reactivate Booking', color: 'text-blue-700' }
        );
        break;
      case 'no_show':
        actions.push(
          { action: 'confirmed', label: 'Reactivate Booking', color: 'text-blue-700' },
          { action: 'cancelled', label: 'Cancel Booking', color: 'text-red-700' }
        );
        break;
    }
    
    return actions;
  };

  const availableActions = getAvailableActions();

  return (
    <div className="relative">
      <div className="flex items-center space-x-2">
        {/* Quick View Button */}
        <button
          onClick={() => onBookingSelect(booking)}
          className="text-blue-600 hover:text-blue-900 text-sm"
          disabled={isLoading}
        >
          View
        </button>

        {/* Quick Actions Dropdown */}
        <div className="relative">
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            disabled={isLoading}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>

          {showDropdown && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
              <div className="py-1">
                {/* Status Actions */}
                {availableActions.length > 0 && (
                  <>
                    <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                      Status Actions
                    </div>
                    {availableActions.map((action) => (
                      <button
                        key={action.action}
                        onClick={() => handleStatusChange(action.action)}
                        className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-50 ${action.color}`}
                      >
                        {action.label}
                      </button>
                    ))}
                  </>
                )}

                {/* Communication Actions */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Communication
                </div>
                
                <a
                  href={`mailto:${booking.customer?.email}`}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Send Email
                </a>
                
                {booking.customer?.phone && (
                  <a
                    href={`tel:${booking.customer.phone}`}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    Call Guest
                  </a>
                )}

                {/* Management Actions */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Management
                </div>
                
                <button
                  onClick={() => {
                    // This would open payment management
                    alert('Payment management coming soon!');
                    setShowDropdown(false);
                  }}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Manage Payment
                </button>

                <button
                  onClick={() => {
                    // This would open modification dialog
                    alert('Booking modification coming soon!');
                    setShowDropdown(false);
                  }}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Modify Booking
                </button>

                <button
                  onClick={() => {
                    // This would generate invoice
                    alert('Invoice generation coming soon!');
                    setShowDropdown(false);
                  }}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Generate Invoice
                </button>

                {/* Dangerous Actions */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Danger Zone
                </div>
                
                <button
                  onClick={handleDelete}
                  className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                >
                  Delete Booking
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
}
