'use client'

import React, { useEffect, useState } from 'react'
import { useCombinedContexts } from '@/hooks/useSafeContext'

/**
 * ProviderValidator - Validates that all required providers are available
 * Shows helpful error messages in development when providers are missing
 */
export default function ProviderValidator({ children }) {
  const [validationState, setValidationState] = useState({
    isValidating: true,
    hasErrors: false,
    errors: []
  })

  const {
    experienceContext,
    markerContext,
    hasExperienceContext,
    hasMarkerContext,
    hasAllContexts
  } = useCombinedContexts()

  useEffect(() => {
    const errors = []

    // Check ExperienceContext
    if (!hasExperienceContext) {
      errors.push({
        type: 'ExperienceContext',
        message: 'ExperienceContextProvider not found in component tree',
        solution: 'Wrap your app with <ExperienceContextProvider>'
      })
    }

    // Check MarkerContext (only for 360° routes)
    if (window.location.pathname.includes('/360s') && !hasMarkerContext) {
      errors.push({
        type: 'MarkerContext',
        message: 'Marker<PERSON>ontextProvider not found in 360° route',
        solution: 'Wrap 360° layout with <MarkerContextProvider>'
      })
    }

    setValidationState({
      isValidating: false,
      hasErrors: errors.length > 0,
      errors
    })
  }, [hasExperienceContext, hasMarkerContext])

  // Only show validation in development
  if (process.env.NODE_ENV !== 'development') {
    return children
  }

  // Show loading state during validation
  if (validationState.isValidating) {
    return (
      <div className="fixed top-4 right-4 z-50 bg-blue-600 text-white px-4 py-2 rounded-lg">
        <div className="flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          <span className="text-sm">Validating providers...</span>
        </div>
      </div>
    )
  }

  // Show errors if any
  if (validationState.hasErrors) {
    return (
      <>
        {children}
        <div className="fixed top-4 right-4 z-50 bg-red-600 text-white p-4 rounded-lg max-w-md">
          <h3 className="font-bold mb-2">Provider Validation Errors</h3>
          {validationState.errors.map((error, index) => (
            <div key={index} className="mb-3 last:mb-0">
              <div className="font-medium text-red-200">{error.type}:</div>
              <div className="text-sm">{error.message}</div>
              <div className="text-xs text-red-300 mt-1">
                Solution: {error.solution}
              </div>
            </div>
          ))}
        </div>
      </>
    )
  }

  // Show success state briefly
  return (
    <>
      {children}
      <div className="fixed top-4 right-4 z-50 bg-green-600 text-white px-4 py-2 rounded-lg opacity-75">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded-full bg-green-400"></div>
          <span className="text-sm">All providers validated ✓</span>
        </div>
      </div>
    </>
  )
}
