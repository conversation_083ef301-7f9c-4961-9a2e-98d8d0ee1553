'use client';

import React, { useState, useEffect } from 'react';
import { loadImageWithCache } from '@/lib/asset-loader';

export default function ImageWrapperResponsive({
  src,
  alt,
  className,
  onLoad,
  onError,
  fallbackSrc = null,
  showLoadingSpinner = false,
  ...props
}) {
  const [imageState, setImageState] = useState({
    loading: true,
    error: null,
    currentSrc: src,
    loadedImage: null,
  });

  useEffect(() => {
    if (!src) {
      setImageState({
        loading: false,
        error: new Error('No source provided'),
        currentSrc: null,
        loadedImage: null,
      });
      return;
    }

    setImageState(prev => ({ ...prev, loading: true, error: null }));

    // Use enhanced asset loader with caching, retry logic, and production URL resolution
    loadImageWithCache(src, {
      maxRetries: 2,
      retryDelay: 500,
      timeout: 8000,
      id: src.split('/').pop()?.split('.')[0] || 'unknown', // Extract filename as ID
    })
      .then(image => {
        setImageState({
          loading: false,
          error: null,
          currentSrc: src,
          loadedImage: image,
        });
        onLoad?.(image);
      })
      .catch(error => {
        console.warn('Image loading failed:', src, error);

        // Try fallback if available
        if (fallbackSrc && fallbackSrc !== src) {
          loadImageWithCache(fallbackSrc)
            .then(image => {
              setImageState({
                loading: false,
                error: null,
                currentSrc: fallbackSrc,
                loadedImage: image,
              });
              onLoad?.(image);
            })
            .catch(fallbackError => {
              setImageState({
                loading: false,
                error: fallbackError,
                currentSrc: src,
                loadedImage: null,
              });
              onError?.(fallbackError);
            });
        } else {
          setImageState({
            loading: false,
            error,
            currentSrc: src,
            loadedImage: null,
          });
          onError?.(error);
        }
      });
  }, [src, fallbackSrc, onLoad, onError]);

  const { loading, error, currentSrc } = imageState;

  return (
    <div className="responsive-img image-container relative">
      {loading && showLoadingSpinner && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-600"></div>
        </div>
      )}

      {error && !loading ? (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200 text-gray-500 text-sm">
          Failed to load image
        </div>
      ) : (
        <img
          src={currentSrc}
          alt={alt}
          className={`responsive-img ${className || ''} ${loading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          {...props}
        />
      )}
    </div>
  );
}
