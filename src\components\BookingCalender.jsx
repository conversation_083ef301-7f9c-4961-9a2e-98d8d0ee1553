'use client'

import React, { useState, useEffect } from 'react'
// import DatePicker from 'react-datepicker'
import { MdOutlineDateRange } from 'react-icons/md'
import "react-datepicker/dist/react-datepicker.css";

import dynamic from 'next/dynamic'

const DatePicker = dynamic(() => import('react-datepicker'), {
  ssr: false,
})

export default function BookingCalender() {
     const [startDate, setStartDate] = useState(new Date())
     const [lastDate, setLastDate] = useState(new Date())
     const [reservationList, setReservationList] = useState([])

     useEffect(() => {
        // const data=
        const handleStatus = async () => {
          try {
            const res=await fetch('/api/reservations', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
              },
            });
            
            if (!res.ok) {
              throw new Error(`HTTP error! status: ${res.status}`);
            }
            
            const data = await res.json();
            setReservationList(data)
            // setSuccess(data.message);
          } catch (error) {
            console.log(error.message);
          }
        }
        handleStatus()
     }, [])
     
     
     console.log('BookingCalender:',reservationList)

  return (
    <>
        <div className="flex items-center w-1/2 h-12 gap-5 mt-1 border-6 text-white border-white rounded-xl cursor-pointer">
            <DatePicker
                showIcon
                // selectsMultiple
                icon={<MdOutlineDateRange className="w-10 h-12 mt-[2px] bg-white/50 p-1"/>}
                selected={startDate}
                onChange={(date) => setStartDate(date)}
                highlightDates={reservationList.map(i=>new Date(i.dateOfStay))}
                className='flex ml-8 w-full outline-none items-center gap-2'
            />
        </div>
        <div className="flex items-center w-1/2 h-12 gap-5 mt-1 border-6 text-white border-white rounded-xl cursor-pointer">
            <DatePicker
                showIcon
                // selectsMultiple
                icon={<MdOutlineDateRange className="w-10 h-12 mt-[2px] bg-white/50 p-1"/>}
                selected={lastDate}
                onChange={(date) => setLastDate(date)}
                highlightDates={reservationList.map(i=>new Date(i.dateOfStay))}
                className='flex ml-8 w-full outline-none items-center gap-2 '
            />
        </div>
    </>
  )
}
