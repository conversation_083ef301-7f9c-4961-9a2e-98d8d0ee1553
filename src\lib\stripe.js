import Stripe from 'stripe';
import { loadStripe } from '@stripe/stripe-js';

// Server-side Stripe instance
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

// Client-side Stripe instance
let stripePromise;
const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
  }
  return stripePromise;
};

/**
 * Create payment intent for booking
 * @param {Object} booking - Booking details
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Payment intent
 */
export async function createBookingPaymentIntent(booking, options = {}) {
  try {
    const {
      paymentMethodId,
      customerId,
      automaticPaymentMethods = true,
      captureMethod = 'automatic',
      confirmationMethod = 'automatic'
    } = options;

    const paymentIntentData = {
      amount: Math.round(booking.pricing.totalAmount * 100), // Convert to cents
      currency: booking.pricing.currency.toLowerCase(),
      metadata: {
        type: 'booking',
        bookingId: booking._id.toString(),
        bookingNumber: booking.bookingNumber,
        customerId: booking.customer.toString(),
        packageId: booking.package.toString(),
      },
      description: `Elephant Island Lodge - Booking ${booking.bookingNumber}`,
      receipt_email: booking.customer.email,
      capture_method: captureMethod,
      confirmation_method: confirmationMethod,
    };

    // Add customer if provided
    if (customerId) {
      paymentIntentData.customer = customerId;
    }

    // Add payment method if provided
    if (paymentMethodId) {
      paymentIntentData.payment_method = paymentMethodId;
      paymentIntentData.confirm = true;
    }

    // Enable automatic payment methods
    if (automaticPaymentMethods) {
      paymentIntentData.automatic_payment_methods = {
        enabled: true,
      };
    }

    const paymentIntent = await stripe.paymentIntents.create(paymentIntentData);

    return paymentIntent;
  } catch (error) {
    console.error('Error creating booking payment intent:', error);
    throw error;
  }
}

/**
 * Create payment intent for store purchase
 * @param {Object} purchase - Purchase details
 * @returns {Promise<Object>} Payment intent
 */
export async function createStorePurchasePaymentIntent(purchase) {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(purchase.pricing.total * 100), // Convert to cents
      currency: purchase.pricing.currency.toLowerCase(),
      metadata: {
        type: 'store_purchase',
        purchaseId: purchase._id.toString(),
        userId: purchase.user.toString(),
      },
      description: `Store purchase: ${purchase.purchaseNumber}`,
    });

    return paymentIntent;
  } catch (error) {
    console.error('Error creating store purchase payment intent:', error);
    throw error;
  }
}

/**
 * Confirm payment intent
 * @param {string} paymentIntentId - Payment intent ID
 * @returns {Promise<Object>} Payment intent
 */
export async function confirmPaymentIntent(paymentIntentId) {
  try {
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    return paymentIntent;
  } catch (error) {
    console.error('Error confirming payment intent:', error);
    throw error;
  }
}

/**
 * Create refund
 * @param {string} chargeId - Charge ID
 * @param {number} amount - Refund amount in cents
 * @param {string} reason - Refund reason
 * @returns {Promise<Object>} Refund object
 */
export async function createRefund(chargeId, amount, reason = 'requested_by_customer') {
  try {
    const refund = await stripe.refunds.create({
      charge: chargeId,
      amount: Math.round(amount * 100), // Convert to cents
      reason,
    });
    
    return refund;
  } catch (error) {
    console.error('Error creating refund:', error);
    throw error;
  }
}

/**
 * Handle webhook events
 * @param {Object} event - Stripe webhook event
 * @returns {Promise<void>}
 */
export async function handleWebhookEvent(event) {
  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;
      case 'charge.dispute.created':
        await handleChargeDispute(event.data.object);
        break;
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  } catch (error) {
    console.error('Error handling webhook event:', error);
    throw error;
  }
}

/**
 * Handle successful payment
 * @param {Object} paymentIntent - Payment intent object
 */
async function handlePaymentSucceeded(paymentIntent) {
  const { metadata } = paymentIntent;
  
  if (metadata.type === 'booking') {
    // Update booking status
    const { Booking } = await import('../models/Booking.js');
    await Booking.findByIdAndUpdate(metadata.bookingId, {
      'payment.status': 'paid',
      'payment.paidAt': new Date(),
      'payment.stripePaymentIntentId': paymentIntent.id,
      'payment.stripeChargeId': paymentIntent.latest_charge,
      status: 'confirmed',
    });
  } else if (metadata.type === 'store_purchase') {
    // Update store purchase status - implement when store purchase model is created
    console.log('Store purchase payment succeeded:', metadata.purchaseId);
    // TODO: Implement store purchase status update when model is available
  }
}

/**
 * Handle failed payment
 * @param {Object} paymentIntent - Payment intent object
 */
async function handlePaymentFailed(paymentIntent) {
  const { metadata } = paymentIntent;
  
  if (metadata.type === 'booking') {
    const { Booking } = await import('../models/Booking.js');
    await Booking.findByIdAndUpdate(metadata.bookingId, {
      'payment.status': 'failed',
      status: 'cancelled',
    });
  } else if (metadata.type === 'store_purchase') {
    // Update store purchase status - implement when store purchase model is created
    console.log('Store purchase payment failed:', metadata.purchaseId);
    // TODO: Implement store purchase status update when model is available
  }
}

/**
 * Handle charge dispute
 * @param {Object} dispute - Dispute object
 */
async function handleChargeDispute(dispute) {
  // Log dispute for manual review
  console.log('Charge dispute created:', dispute);
  // Implement dispute handling logic here
}

/**
 * Create or retrieve Stripe customer
 * @param {Object} user - User object
 * @returns {Promise<Object>} Stripe customer
 */
export async function createOrRetrieveCustomer(user) {
  try {
    // Check if customer already exists
    const customers = await stripe.customers.list({
      email: user.email,
      limit: 1,
    });

    if (customers.data.length > 0) {
      return customers.data[0];
    }

    // Create new customer
    const customer = await stripe.customers.create({
      email: user.email,
      name: user.name,
      phone: user.phone,
      metadata: {
        userId: user._id.toString(),
        role: user.role,
      },
    });

    return customer;
  } catch (error) {
    console.error('Error creating/retrieving customer:', error);
    throw error;
  }
}

/**
 * Create setup intent for saving payment methods
 * @param {string} customerId - Stripe customer ID
 * @returns {Promise<Object>} Setup intent
 */
export async function createSetupIntent(customerId) {
  try {
    const setupIntent = await stripe.setupIntents.create({
      customer: customerId,
      payment_method_types: ['card'],
      usage: 'off_session',
    });

    return setupIntent;
  } catch (error) {
    console.error('Error creating setup intent:', error);
    throw error;
  }
}

/**
 * Get customer payment methods
 * @param {string} customerId - Stripe customer ID
 * @returns {Promise<Array>} Payment methods
 */
export async function getCustomerPaymentMethods(customerId) {
  try {
    const paymentMethods = await stripe.paymentMethods.list({
      customer: customerId,
      type: 'card',
    });

    return paymentMethods.data;
  } catch (error) {
    console.error('Error getting payment methods:', error);
    throw error;
  }
}

/**
 * Detach payment method from customer
 * @param {string} paymentMethodId - Payment method ID
 * @returns {Promise<Object>} Payment method
 */
export async function detachPaymentMethod(paymentMethodId) {
  try {
    const paymentMethod = await stripe.paymentMethods.detach(paymentMethodId);
    return paymentMethod;
  } catch (error) {
    console.error('Error detaching payment method:', error);
    throw error;
  }
}

/**
 * Create refund with enhanced options
 * @param {string} paymentIntentId - Payment intent ID
 * @param {number} amount - Refund amount in dollars
 * @param {Object} options - Refund options
 * @returns {Promise<Object>} Refund object
 */
export async function createRefundEnhanced(paymentIntentId, amount, options = {}) {
  try {
    const {
      reason = 'requested_by_customer',
      metadata = {},
      refundApplicationFee = false,
      reverseTransfer = false,
    } = options;

    const refundData = {
      payment_intent: paymentIntentId,
      amount: Math.round(amount * 100), // Convert to cents
      reason,
      metadata,
    };

    if (refundApplicationFee) {
      refundData.refund_application_fee = true;
    }

    if (reverseTransfer) {
      refundData.reverse_transfer = true;
    }

    const refund = await stripe.refunds.create(refundData);
    return refund;
  } catch (error) {
    console.error('Error creating refund:', error);
    throw error;
  }
}

/**
 * Calculate Stripe fees
 * @param {number} amount - Transaction amount in dollars
 * @param {string} currency - Currency code
 * @param {string} country - Country code
 * @returns {Object} Fee breakdown
 */
export function calculateStripeFees(amount, currency = 'usd', country = 'US') {
  const amountCents = Math.round(amount * 100);

  // Stripe fees vary by country and payment method
  // These are approximate fees for US cards
  const fixedFee = 30; // 30 cents
  const percentageFee = 0.029; // 2.9%

  const stripeFee = Math.round(amountCents * percentageFee + fixedFee);
  const netAmount = amountCents - stripeFee;

  return {
    grossAmount: amountCents,
    stripeFee,
    netAmount,
    feePercentage: percentageFee,
    fixedFee,
  };
}

/**
 * Format Stripe amount (cents to dollars)
 * @param {number} amountCents - Amount in cents
 * @param {string} currency - Currency code
 * @returns {string} Formatted amount
 */
export function formatStripeAmount(amountCents, currency = 'USD') {
  const amount = amountCents / 100;
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amount);
}

/**
 * Validate webhook signature
 * @param {string} payload - Request body
 * @param {string} signature - Stripe signature header
 * @param {string} secret - Webhook secret
 * @returns {Object} Stripe event
 */
export function validateWebhookSignature(payload, signature, secret) {
  try {
    return stripe.webhooks.constructEvent(payload, signature, secret);
  } catch (error) {
    console.error('Webhook signature validation failed:', error);
    throw error;
  }
}

/**
 * Calculate application fee (for marketplace scenarios)
 * @param {number} amount - Transaction amount
 * @returns {number} Application fee
 */
export function calculateApplicationFee(amount) {
  // Example: 3% application fee
  return Math.round(amount * 0.03);
}

export { stripe, getStripe };
