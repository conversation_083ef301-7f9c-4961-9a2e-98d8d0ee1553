'use client'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import { useEffect, useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import SpinerComponent from '../SpinerComponent'

export default function ItemInfoComponent() {
  const [error,setError]=useState('')
  const [showError,setShowError]=useState(false)
  const [loading,setLoading]=useState(false)
  const [data,setData]=useState(null)
  const {experienceState,disptachExperience}=useContextExperience()

  const fetchData = async (id) => {
    try {
      setLoading(true)
      const serverResponse=await fetch(`/api/info-markers/${id}`)
      const responseData=await serverResponse.json()
      if(!data){
        setError('Failed to load data')
        setShowError(true)
      }
      // console.log(responseData?.data)
      setData(responseData?.data)
      setLoading(false)
      // return responseData
    } catch (error) {
      console.log(error)
      setError(error.message)
      setShowError(true)
    }
  }

  useEffect(() => {
    fetchData(experienceState?.showItemInfo?.id)
  }, [experienceState?.showItemInfo?.id])

  // console.log('ItemInfoComponent:',data?.data)
  
  return (
    <div className='flex w-full h-fit text-white'>
      {/* {showError
        ? <div className='flex w-full h-full items-center justify-center'>{error}</div> 
        : <div className='flex w-full h-full items-start justify-start'>
            {loading 
              ? <div className='text-center'><span className='text-sm'>loading...</span></div>  
              : <div className='flex w-full h-full items-start justify-start'>
                  <ImageWrapperResponsive src={data?.image}/>
                </div>
            }
          </div>
      } */}
      {loading 
        ? <div className='flex w-full h-full items-center justify-center'><SpinerComponent/></div>  
        : <div className='flex w-full h-full flex-col items-start justify-start mt-16'>
            <ImageWrapperResponsive src={data?.image} className='w-full h-full'/>
          </div>
      }
    </div>
  )
}
