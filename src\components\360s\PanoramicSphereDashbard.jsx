'use client';

import { useRef, useEffect, useState, useMemo, useCallback, memo } from 'react';
import { OrbitControls } from '@react-three/drei';
import * as THREE from 'three';
import { useControls } from 'leva';
import { useThree } from '@react-three/fiber'; // Import useThree to get access to camera

// Memoized OrbitControls component to prevent unnecessary re-renders
const MemoizedOrbitControls = memo(OrbitControls);

function PanoramicSphere({
  currentImage,
  set_360Object, // This prop allows updating the parent state
  imageUrl,
  imageId,
  textureCache,
  setTextureCache,
  loadingQueue,
  setLoadingQueue,
  onTextureLoad,
  resetView,
}) {
  const meshRef = useRef();
  const controlsRef = useRef();
  const debounceTimeoutRef = useRef(null);
  const [currentTexture, setCurrentTexture] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const { camera } = useThree(); // Get the R3F camera instance

  // Memoized geometry and material for better performance
  const sphereGeometry = useMemo(() => new THREE.SphereGeometry(32, 60, 40), []);

  const basicMaterial = useMemo(() => {
    const material = new THREE.MeshBasicMaterial({
      side: THREE.BackSide,
      transparent: false,
    });
    return material;
  }, []);

  // Memoize the texture loader to avoid re-creation
  const textureLoader = useMemo(() => new THREE.TextureLoader(), []);

  // Memoized OrbitControls configuration
  const controlsConfig = useMemo(() => ({
    enableZoom: false,
    enablePan: false,
    enableRotate: true,
    enableDamping: true,
    dampingFactor: 0.05,
    rotateSpeed: -0.35,
    minPolarAngle: 0,
    maxPolarAngle: Math.PI,
    minAzimuthAngle: -Infinity,
    maxAzimuthAngle: Infinity,
  }), []);

  // Cleanup geometries, materials, textures, and timeouts on unmount
  useEffect(() => {
    return () => {
      sphereGeometry?.dispose();
      basicMaterial?.dispose();
      currentTexture?.dispose(); // Dispose of the texture if it exists
      // Cleanup debounce timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [sphereGeometry, basicMaterial, currentTexture]);

  // Optimized texture configuration function
  const configureTexture = useCallback((texture) => {
    texture.mapping = THREE.EquirectangularReflectionMapping;
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;
    texture.minFilter = THREE.LinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.flipY = true;
    // Set proper color space for accurate color representation
    texture.colorSpace = THREE.SRGBColorSpace;
    texture.needsUpdate = true;
    return texture;
  }, []);

  // Enhanced texture loading with production-ready error handling and URL validation
  const loadTexture = useCallback(async (url, id) => {
    // Validate URL before attempting to load
    if (!url || typeof url !== 'string') {
      console.error(`PanoramicSphereDashboard - Invalid URL for ID ${id}:`, url);
      return null;
    }

    // Check if we have a cached texture for this ID
    if (textureCache.has(id)) {
      const cachedEntry = textureCache.get(id);

      // If cached entry has URL validation and URLs match, return cached texture
      if (cachedEntry && cachedEntry.url === url) {
        return cachedEntry.texture;
      }

      // If URLs don't match, invalidate the cache entry
      if (cachedEntry && cachedEntry.url !== url) {
        console.log(`Cache invalidation: URL changed for ID ${id}`);
        setTextureCache(prevCache => {
          const newCache = new Map(prevCache);
          newCache.delete(id);
          return newCache;
        });
      }
    }

    setIsLoading(true);

    try {
      // Validate and normalize URL for production
      const normalizedUrl = normalizeImageUrl(url);

      const texture = await new Promise((resolve, reject) => {
        const loader = textureLoader;

        const timeoutId = setTimeout(() => {
          reject(new Error(`Texture loading timeout for ID ${id}`));
        }, 15000); // Increased timeout for production

        // Add progress tracking for better debugging
        loader.load(
          normalizedUrl,
          (loadedTexture) => {
            clearTimeout(timeoutId);
            configureTexture(loadedTexture);
            console.log(`Successfully loaded texture for ID ${id}`);
            resolve(loadedTexture);
          },
          (progress) => {
            // Optional: Log progress for debugging
            if (progress.lengthComputable) {
              const percentComplete = (progress.loaded / progress.total) * 100;
              console.log(`Loading texture ${id}: ${percentComplete.toFixed(1)}%`);
            }
          },
          (error) => {
            clearTimeout(timeoutId);
            console.error(`Failed to load texture for ID ${id} from URL ${normalizedUrl}:`, error);
            reject(error);
          }
        );
      });

      setTextureCache(prevCache => {
        const newCache = new Map(prevCache);
        // Store texture with URL for cache validation
        newCache.set(id, { texture, url: normalizedUrl });
        return newCache;
      });

      return texture;
    } catch (error) {
      console.error(`PanoramicSphereDashboard - Error loading texture for ID ${id}:`, error);

      // Try fallback URL if available
      const fallbackUrl = getFallbackImageUrl(id);
      if (fallbackUrl && fallbackUrl !== url) {
        console.log(`Attempting fallback URL for ID ${id}: ${fallbackUrl}`);
        try {
          return await loadTexture(fallbackUrl, id);
        } catch (fallbackError) {
          console.error(`Fallback also failed for ID ${id}:`, fallbackError);
        }
      }

      return null;
    } finally {
      setIsLoading(false);
    }
  }, [textureCache, setTextureCache, textureLoader, configureTexture, setIsLoading]);

  // URL normalization for production compatibility
  const normalizeImageUrl = useCallback((url) => {
    if (!url) return null;

    // If it's already a full URL (Firebase, etc.), return as-is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // If it's a relative path, ensure it starts with /
    if (!url.startsWith('/')) {
      return `/${url}`;
    }

    return url;
  }, []);

  // Fallback image URL generation
  const getFallbackImageUrl = useCallback((id) => {
    // Try common fallback patterns
    const fallbacks = [
      `/uploads/360s/${id}.jpg`,
      `/uploads/360s/${id}.png`,
      `/assets/360s/${id}.jpg`,
      `/assets/360s/default-360.jpg`
    ];

    // Return first fallback for now
    return fallbacks[0];
  }, []);

  // Optimized background texture loading with better performance
  useEffect(() => {
    if (loadingQueue.length === 0) return;

    let isCancelled = false;

    const loadNextTexture = async () => {
      if (isCancelled) return;

      const nextItem = loadingQueue[0];
      if (!nextItem) return;

      // Check if texture is cached and URL matches
      if (textureCache.has(nextItem._id)) {
        const cachedEntry = textureCache.get(nextItem._id);
        if (cachedEntry && cachedEntry.url === nextItem.url) {
          setLoadingQueue(prev => prev.slice(1));
          return;
        }
      }

      try {
        setLoadingQueue(prev =>
          prev.map(item =>
            item._id === nextItem._id
              ? { ...item, status: 'downloading' }
              : item
          )
        );

        if (!isCancelled) {
          await loadTexture(nextItem.url, nextItem._id);
        }

        if (!isCancelled) {
          setLoadingQueue(prev =>
            prev.filter(item => item._id !== nextItem._id)
          );
        }
      } catch (error) {
        console.error(`Background texture loading failed for ID ${nextItem._id}:`, error);
        if (!isCancelled) {
          setLoadingQueue(prev =>
            prev.filter(item => item._id !== nextItem._id)
          );
        }
      }
    };

    const scheduleLoad = () => {
      if (typeof window !== 'undefined' && window.requestIdleCallback) {
        window.requestIdleCallback(loadNextTexture, { timeout: 1000 });
      } else {
        setTimeout(loadNextTexture, 100);
      }
    };

    scheduleLoad();

    return () => {
      isCancelled = true;
    };
  }, [loadingQueue, textureCache, setLoadingQueue, loadTexture]);

  // Optimized current texture loading with better state management and material assignment
  useEffect(() => {
    if (!imageUrl || !imageId) {
      setCurrentTexture(null);
      // Clear material texture when no image
      if (basicMaterial) {
        basicMaterial.map = null;
        basicMaterial.needsUpdate = true;
      }
      return;
    }

    let isCancelled = false;

    const loadCurrentTexture = async () => {
      try {
        const texture = await loadTexture(imageUrl, imageId);
        if (texture && !isCancelled) {
          setCurrentTexture(texture);
          // Assign texture to material immediately to prevent visual glitches
          if (basicMaterial) {
            basicMaterial.map = texture;
            basicMaterial.needsUpdate = true;
          }
          onTextureLoad?.();
        } else if (!isCancelled) {
          setCurrentTexture(null);
          if (basicMaterial) {
            basicMaterial.map = null;
            basicMaterial.needsUpdate = true;
          }
        }
      } catch (error) {
        console.error('Error loading current texture:', error);
        if (!isCancelled) {
          setCurrentTexture(null);
          if (basicMaterial) {
            basicMaterial.map = null;
            basicMaterial.needsUpdate = true;
          }
        }
      }
    };

    loadCurrentTexture();

    return () => {
      isCancelled = true;
    };
  }, [imageUrl, imageId, loadTexture, onTextureLoad, basicMaterial]);

  // Update material when texture changes
  useEffect(() => {
    if (basicMaterial && currentTexture) {
      basicMaterial.map = currentTexture;
      basicMaterial.needsUpdate = true;
    } else if (basicMaterial) {
      basicMaterial.map = null;
      basicMaterial.needsUpdate = true;
    }
  }, [basicMaterial, currentTexture]);

  // Reset OrbitControls when resetView prop is true
  useEffect(() => {
    if (controlsRef.current && resetView) {
      controlsRef.current.reset();
    }
  }, [resetView]);

  // Leva Controls with throttling:
  const [, setControls] = useControls('Controls', () => ({
    cameraPosition: {
      value: 0,
      min: -0.075,
      max: 0.075,
      step: 0.001,
      transient: false, // Disable transient updates for better performance
      onChange: (value) => {
        // Throttle updates to reduce message handler load
        if (debounceTimeoutRef.current) {
          clearTimeout(debounceTimeoutRef.current);
        }

        debounceTimeoutRef.current = setTimeout(() => {
          set_360Object(prev => ({ ...prev, cameraPosition: value }));
          // Manually update OrbitControls target for immediate visual feedback
          if (controlsRef.current) {
            controlsRef.current.target.y = value;
            controlsRef.current.update();
          }
        }, 50); // 50ms throttle
      }
    },
    _360Rotation: {
      value: 0,
      min: 0,
      max: Math.PI * 2,
      step: 0.01,
      transient: false, // Disable transient updates for better performance
      onChange: (value) => {
        // Throttle updates to reduce message handler load
        if (debounceTimeoutRef.current) {
          clearTimeout(debounceTimeoutRef.current);
        }

        debounceTimeoutRef.current = setTimeout(() => {
          set_360Object(prev => ({ ...prev, _360Rotation: value }));
          // Manually update mesh rotation for immediate visual feedback
          if (meshRef.current) {
            meshRef.current.rotation.y = value;
          }
        }, 50); // 50ms throttle
      }
    }
  }), []);

  // Synchronize Leva controls with the incoming currentImage prop.
  useEffect(() => {
    if (currentImage) {
      setControls({
        cameraPosition: currentImage.cameraPosition,
        _360Rotation: currentImage._360Rotation,
      });
      // Also update OrbitControls target and mesh rotation directly when new image loads
      if (controlsRef.current) {
        controlsRef.current.target.y = currentImage.cameraPosition;
        controlsRef.current.update();
      }
      if (meshRef.current) {
        meshRef.current.rotation.y = currentImage._360Rotation;
      }
    }
  }, [currentImage, setControls]);

  // NEW: Listen to OrbitControls' 'change' event to update Leva and parent state
  useEffect(() => {
    const controls = controlsRef.current;
    if (!controls || !meshRef.current) return;

    const handleControlsChange = () => {
      // Get the camera's current position and rotation
      // The mesh's rotation is what _360Rotation controls in this setup.
      // OrbitControls primarily controls the camera's orientation relative to the target.
      // The camera's position relative to its target defines the "cameraPosition" offset.

      // Calculate cameraPosition (vertical offset from target)
      const currentCameraY = camera.position.y;
      const targetY = controls.target.y;
      const newCameraPosition = currentCameraY - targetY; // How much camera is above/below its target

      // Get the current panorama rotation from the mesh
      const new360Rotation = meshRef.current.rotation.y;

      // Update Leva UI
      setControls({
        cameraPosition: newCameraPosition,
        _360Rotation: new360Rotation,
      });

      // Update parent state
      set_360Object(prev => ({
        ...prev,
        cameraPosition: newCameraPosition,
        _360Rotation: new360Rotation,
      }));
    };

    controls.addEventListener('change', handleControlsChange);

    return () => {
      controls.removeEventListener('change', handleControlsChange);
    };
  }, [camera, setControls, set_360Object]); // Depend on camera, setControls, and set_360Object

  // Render nothing if texture is not yet loaded (show loading state)
  if (!currentTexture) {
    return (
      <MemoizedOrbitControls
        ref={controlsRef}
        {...controlsConfig}
        target-y={currentImage?.cameraPosition || 0}
      />
    );
  }

  return (
    <>
      {/* Optimized OrbitControls for camera interaction */}
      <MemoizedOrbitControls
        ref={controlsRef}
        {...controlsConfig}
        target-y={currentImage?.cameraPosition || 0}
      />

      {/* Optimized panoramic sphere mesh */}
      <mesh
        ref={meshRef}
        rotation={[0, currentImage?._360Rotation || 0, 0]} // Initial rotation from prop
        scale={[1, 1, -1]} // Standard scale for panoramic sphere
      >
        <primitive object={sphereGeometry} />
        <primitive object={basicMaterial} />
      </mesh>
    </>
  );
}

// Export memoized component for better performance
export default memo(PanoramicSphere);