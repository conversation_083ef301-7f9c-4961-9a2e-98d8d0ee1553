'use client';

import { useState, useEffect, useCallback } from 'react';

export default function PackageAvailability({ package: pkg, onPackageUpdate, onHasChanges }) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    availability: {
      isActive: pkg.availability?.isActive ?? true,
      advanceBookingDays: pkg.availability?.advanceBookingDays || 1,
      maxAdvanceBookingDays: pkg.availability?.maxAdvanceBookingDays || 365,
      seasonalAvailability: pkg.availability?.seasonalAvailability || [],
      blackoutDates: pkg.availability?.blackoutDates || [],
    },
    requirements: {
      minimumAge: pkg.requirements?.minimumAge || 0,
      fitnessLevel: pkg.requirements?.fitnessLevel || 'easy',
      specialRequirements: pkg.requirements?.specialRequirements || [],
    },
  });

  const [originalData, setOriginalData] = useState(formData);
  const [newSeasonalPeriod, setNewSeasonalPeriod] = useState({
    startDate: '',
    endDate: '',
    isAvailable: true,
    reason: '',
  });
  const [newBlackoutDate, setNewBlackoutDate] = useState({
    date: '',
    reason: '',
  });
  const [newRequirement, setNewRequirement] = useState('');

  useEffect(() => {
    const hasChanges = JSON.stringify(formData) !== JSON.stringify(originalData);
    onHasChanges(hasChanges);
  }, [formData, originalData, onHasChanges]);

  const handleAvailabilityChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      availability: {
        ...prev.availability,
        [field]: field === 'isActive' ? value : (field.includes('Days') ? Number(value) : value),
      },
    }));
  };

  const handleRequirementsChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      requirements: {
        ...prev.requirements,
        [field]: field === 'minimumAge' ? Number(value) : value,
      },
    }));
  };

  const addSeasonalPeriod = () => {
    if (!newSeasonalPeriod.startDate || !newSeasonalPeriod.endDate) {
      setError('Please provide both start and end dates for the seasonal period');
      return;
    }

    const startDate = new Date(newSeasonalPeriod.startDate);
    const endDate = new Date(newSeasonalPeriod.endDate);

    if (startDate >= endDate) {
      setError('End date must be after start date');
      return;
    }

    setFormData(prev => ({
      ...prev,
      availability: {
        ...prev.availability,
        seasonalAvailability: [
          ...prev.availability.seasonalAvailability,
          {
            ...newSeasonalPeriod,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
        ],
      },
    }));

    setNewSeasonalPeriod({
      startDate: '',
      endDate: '',
      isAvailable: true,
      reason: '',
    });
    setError(null);
  };

  const removeSeasonalPeriod = (index) => {
    setFormData(prev => ({
      ...prev,
      availability: {
        ...prev.availability,
        seasonalAvailability: prev.availability.seasonalAvailability.filter((_, i) => i !== index),
      },
    }));
  };

  const addBlackoutDate = () => {
    if (!newBlackoutDate.date) {
      setError('Please provide a date for the blackout');
      return;
    }

    setFormData(prev => ({
      ...prev,
      availability: {
        ...prev.availability,
        blackoutDates: [
          ...prev.availability.blackoutDates,
          {
            ...newBlackoutDate,
            date: new Date(newBlackoutDate.date).toISOString(),
          },
        ],
      },
    }));

    setNewBlackoutDate({
      date: '',
      reason: '',
    });
    setError(null);
  };

  const removeBlackoutDate = (index) => {
    setFormData(prev => ({
      ...prev,
      availability: {
        ...prev.availability,
        blackoutDates: prev.availability.blackoutDates.filter((_, i) => i !== index),
      },
    }));
  };

  const addSpecialRequirement = () => {
    if (!newRequirement.trim()) {
      setError('Please enter a requirement');
      return;
    }

    setFormData(prev => ({
      ...prev,
      requirements: {
        ...prev.requirements,
        specialRequirements: [
          ...prev.requirements.specialRequirements,
          newRequirement.trim(),
        ],
      },
    }));

    setNewRequirement('');
    setError(null);
  };

  const removeSpecialRequirement = (index) => {
    setFormData(prev => ({
      ...prev,
      requirements: {
        ...prev.requirements,
        specialRequirements: prev.requirements.specialRequirements.filter((_, i) => i !== index),
      },
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/packages/${pkg._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        onPackageUpdate(data.data);
        setOriginalData(formData);
      } else {
        setError(data.message || 'Failed to update availability');
      }
    } catch (err) {
      setError('Failed to update availability');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setFormData(originalData);
    setError(null);
  };

  return (
    <div className="space-y-6">
      {/* Package Status */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Package Status</h3>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.availability.isActive}
                onChange={(e) => handleAvailabilityChange('isActive', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                Package is active and available for booking
              </label>
            </div>
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
              formData.availability.isActive
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {formData.availability.isActive ? 'Active' : 'Inactive'}
            </div>
          </div>
        </div>
      </div>

      {/* Booking Rules */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Booking Rules</h3>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Advance Booking (Days)
              </label>
              <input
                type="number"
                min="0"
                value={formData.availability.advanceBookingDays}
                onChange={(e) => handleAvailabilityChange('advanceBookingDays', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">Minimum days in advance to book</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum Advance Booking (Days)
              </label>
              <input
                type="number"
                min="1"
                value={formData.availability.maxAdvanceBookingDays}
                onChange={(e) => handleAvailabilityChange('maxAdvanceBookingDays', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">Maximum days in advance to book</p>
            </div>
          </div>
        </div>
      </div>

      {/* Requirements */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Package Requirements</h3>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Age
              </label>
              <input
                type="number"
                min="0"
                value={formData.requirements.minimumAge}
                onChange={(e) => handleRequirementsChange('minimumAge', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fitness Level Required
              </label>
              <select
                value={formData.requirements.fitnessLevel}
                onChange={(e) => handleRequirementsChange('fitnessLevel', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="easy">Easy</option>
                <option value="moderate">Moderate</option>
                <option value="challenging">Challenging</option>
                <option value="expert">Expert</option>
              </select>
            </div>
          </div>

          {/* Special Requirements */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Special Requirements</h4>
            <div className="flex space-x-2 mb-4">
              <input
                type="text"
                value={newRequirement}
                onChange={(e) => setNewRequirement(e.target.value)}
                placeholder="e.g., Swimming ability required"
                className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                onKeyPress={(e) => e.key === 'Enter' && addSpecialRequirement()}
              />
              <button
                type="button"
                onClick={addSpecialRequirement}
                className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Add
              </button>
            </div>

            {formData.requirements.specialRequirements.length > 0 ? (
              <div className="space-y-2">
                {formData.requirements.specialRequirements.map((requirement, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">{requirement}</span>
                    <button
                      type="button"
                      onClick={() => removeSpecialRequirement(index)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">No special requirements</p>
            )}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <button
          type="button"
          onClick={handleReset}
          className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Reset
        </button>
        <button
          type="button"
          onClick={handleSave}
          disabled={isLoading}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Saving...</span>
            </div>
          ) : (
            'Save Availability'
          )}
        </button>
      </div>
    </div>
  );
}
