import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Payment } from '@/models/Payment';
import { Booking } from '@/models/Booking';
import { confirmPaymentIntent } from '@/lib/stripe';

// POST /api/payments/confirm - Confirm payment intent
export async function POST(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    const { paymentIntentId, paymentMethodId } = body;
    
    if (!paymentIntentId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'paymentIntentId is required',
        },
        { status: 400 }
      );
    }
    
    // Find the payment record
    const payment = await Payment.findOne({
      'stripe.paymentIntentId': paymentIntentId,
    }).populate('booking').populate('customer');
    
    if (!payment) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Payment not found',
        },
        { status: 404 }
      );
    }
    
    // Confirm the payment intent
    const paymentIntent = await confirmPaymentIntent(paymentIntentId);
    
    // Update payment status based on payment intent status
    if (paymentIntent.status === 'succeeded') {
      payment.status = 'succeeded';
      payment.processedAt = new Date();
      
      // Update payment method details if available
      if (paymentIntent.charges?.data?.[0]) {
        const charge = paymentIntent.charges.data[0];
        payment.stripe.chargeId = charge.id;
        
        if (charge.payment_method_details?.card) {
          payment.paymentMethod.details.last4 = charge.payment_method_details.card.last4;
          payment.paymentMethod.details.brand = charge.payment_method_details.card.brand;
        }
        
        // Update fees
        payment.fees.stripeFee = charge.application_fee_amount || 0;
        payment.fees.totalFees = payment.fees.stripeFee;
        payment.netAmount = payment.amount - payment.fees.totalFees;
      }
      
      await payment.save();
      
      // Update booking status
      const booking = await Booking.findById(payment.booking);
      if (booking) {
        booking.payment.status = 'paid';
        booking.payment.paidAmount += payment.amount;
        booking.payment.remainingAmount = booking.pricing.totalAmount - booking.payment.paidAmount;
        
        // If fully paid, confirm the booking
        if (booking.payment.remainingAmount <= 0) {
          booking.status = 'confirmed';
        }
        
        await booking.save();
      }
      
      return NextResponse.json({
        success: true,
        data: {
          payment,
          booking,
          paymentIntent: {
            id: paymentIntent.id,
            status: paymentIntent.status,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
          },
        },
        message: 'Payment confirmed successfully',
      });
    } else if (paymentIntent.status === 'requires_action') {
      return NextResponse.json({
        success: false,
        requiresAction: true,
        data: {
          paymentIntent: {
            id: paymentIntent.id,
            status: paymentIntent.status,
            client_secret: paymentIntent.client_secret,
          },
        },
        message: 'Payment requires additional authentication',
      });
    } else if (paymentIntent.status === 'requires_payment_method') {
      return NextResponse.json({
        success: false,
        requiresPaymentMethod: true,
        data: {
          paymentIntent: {
            id: paymentIntent.id,
            status: paymentIntent.status,
            client_secret: paymentIntent.client_secret,
          },
        },
        message: 'Payment method failed, please try another',
      });
    } else {
      // Payment failed
      payment.status = 'failed';
      payment.failedAt = new Date();
      
      if (paymentIntent.last_payment_error) {
        payment.failure = {
          code: paymentIntent.last_payment_error.code,
          message: paymentIntent.last_payment_error.message,
          type: paymentIntent.last_payment_error.type,
        };
      }
      
      await payment.save();
      
      return NextResponse.json(
        {
          success: false,
          error: 'Payment Failed',
          message: paymentIntent.last_payment_error?.message || 'Payment failed',
          data: {
            paymentIntent: {
              id: paymentIntent.id,
              status: paymentIntent.status,
            },
          },
        },
        { status: 402 }
      );
    }
  } catch (error) {
    console.error('Error confirming payment:', error);
    
    if (error.type?.startsWith('Stripe')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Stripe Error',
          message: error.message,
          code: error.code,
        },
        { status: 402 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to confirm payment',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
