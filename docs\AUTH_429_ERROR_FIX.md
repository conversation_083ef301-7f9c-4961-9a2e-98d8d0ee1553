# Auth.js 429 Error Fix - Production Login Issues

## Issue Description
Users experiencing `GET https://localhost:3000/auth/signin 429 (Too Many Requests)` error when attempting to log in on the production version at `https://victorchelemu.com`.

## Root Cause Analysis

### **1. Rate Limiting Issues**
- **Strict Auth Limits**: Auth routes had a limit of only 20 requests per 15 minutes
- **Essential Endpoints**: Critical auth endpoints like `/api/auth/signin`, `/api/auth/session` were being rate limited
- **Production Load**: Production environments typically have higher authentication traffic

### **2. Port Configuration Mismatch**
- **Error Shows**: `localhost:3000` in error messages
- **App Runs On**: `localhost:3001` (correct port)
- **Settings Issue**: Potential configuration inconsistencies

### **3. Production Environment Configuration**
- **NEXTAUTH_URL**: Must be properly configured for production domain
- **Rate Limiting**: Production needs more lenient limits for auth routes

## Solutions Implemented

### **1. Enhanced Rate Limiting Configuration** ✅

#### **Essential Auth Routes Exemption**
```javascript
// Skip rate limiting for essential auth endpoints to prevent login issues
const isEssentialAuthRoute = pathname.startsWith('/api/auth/signin') || 
                            pathname.startsWith('/api/auth/session') ||
                            pathname.startsWith('/api/auth/providers') ||
                            pathname.startsWith('/api/auth/csrf');
```

#### **Production-Aware Rate Limits**
```javascript
if (isAuthRoute) {
  // More lenient limits for auth routes in production to prevent login issues
  currentLimit = process.env.NODE_ENV === 'production' ? 50 : 20;
}
```

#### **Updated Rate Limiting Logic**
```javascript
// Skip rate limiting for media requests and essential auth endpoints
if (!isMediaGetRequest && !isEssentialAuthRoute) {
  // Apply rate limiting logic
  rateLimitResult = rateLimit(ip, currentLimit);
}
```

### **2. Auth.js Configuration Fix** ✅

#### **Corrected Development URL**
```javascript
// Fixed HTTPS to HTTP for localhost development
url: process.env.NEXTAUTH_URL ||
     (process.env.NODE_ENV === 'production'
       ? 'https://victorchelemu.com'
       : 'http://localhost:3001'),  // Changed from https to http
```

### **3. Rate Limiting Headers Update** ✅

#### **Proper Header Management**
```javascript
if (!isMediaGetRequest && !isEssentialAuthRoute) {
  response.headers.set('X-RateLimit-Limit', currentLimit.toString());
  response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
} else {
  // For media GET requests and essential auth routes, indicate no rate limiting
  response.headers.set('X-RateLimit-Limit', 'unlimited');
  response.headers.set('X-RateLimit-Remaining', 'unlimited');
}
```

## Rate Limiting Configuration Summary

### **Current Limits After Fix**
- **Essential Auth Routes**: No rate limiting (unlimited)
- **Other Auth Routes (Development)**: 20 requests per 15 minutes
- **Other Auth Routes (Production)**: 50 requests per 15 minutes
- **360° Routes**: 200 requests per 15 minutes
- **Media GET Requests**: No rate limiting (unlimited)
- **Standard Routes**: 100 requests per 15 minutes

### **Essential Auth Routes (No Limits)**
- `/api/auth/signin` - Sign in endpoint
- `/api/auth/session` - Session validation
- `/api/auth/providers` - Available providers
- `/api/auth/csrf` - CSRF token endpoint

## Testing Instructions

### **1. Development Testing**
```bash
# Start development server
npm run dev

# Test auth endpoints
curl -I http://localhost:3001/api/auth/signin
curl -I http://localhost:3001/api/auth/session
curl -I http://localhost:3001/api/auth/providers
```

### **2. Production Deployment**
```bash
# Build for production
npm run build

# Start production server
npm start

# Verify auth endpoints work without rate limiting
curl -I https://victorchelemu.com/api/auth/signin
```

## Environment Configuration

### **Development (.env.local)**
```env
NEXTAUTH_URL=http://localhost:3001
NODE_ENV=development
```

### **Production (.env.production)**
```env
NEXTAUTH_URL=https://victorchelemu.com
NODE_ENV=production
```

## Monitoring and Prevention

### **1. Rate Limit Monitoring**
- Monitor `X-RateLimit-*` headers in browser dev tools
- Check for 429 responses in network tab
- Essential auth routes should show "unlimited" limits

### **2. Production Health Checks**
- Verify login functionality works for multiple users
- Test OAuth providers (Google, Facebook)
- Monitor authentication success rates

### **3. Error Handling**
- 429 errors on essential auth routes should not occur
- Non-essential auth routes may still hit limits under extreme load
- Users should see proper error messages for rate limit issues

## Future Considerations

### **1. Advanced Rate Limiting**
- Consider implementing Redis-based rate limiting for production
- Add user-specific rate limiting for authenticated users
- Implement exponential backoff for repeated failures

### **2. Monitoring Integration**
- Add logging for rate limit hits
- Monitor authentication success/failure rates
- Alert on unusual authentication patterns

### **3. Load Balancing**
- Consider multiple auth server instances for high load
- Implement session affinity for consistent user experience
- Add health checks for auth service availability

## Troubleshooting

### **Common Issues**
1. **Still getting 429 errors**: Check if using correct domain/port
2. **Auth not working**: Verify NEXTAUTH_URL matches actual domain
3. **Rate limits too strict**: Adjust production limits in middleware.js

### **Debug Commands**
```bash
# Check current environment
echo $NODE_ENV

# Verify auth configuration
npm run verify-auth-config

# Test middleware
npm run test-middleware
```
