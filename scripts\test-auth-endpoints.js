#!/usr/bin/env node

/**
 * Auth Endpoints Testing Script
 * 
 * This script tests the authentication endpoints to verify they are working
 * correctly and not being rate limited.
 */

const https = require('https');
const http = require('http');

// Configuration
const isDevelopment = process.env.NODE_ENV !== 'production';
const baseUrl = isDevelopment ? 'https://localhost:3001' : 'https://victorchelemu.com';
const protocol = https; // Always use HTTPS

// Test endpoints
const endpoints = [
  '/api/auth/signin',
  '/api/auth/session', 
  '/api/auth/providers',
  '/api/auth/csrf',
  '/api/auth/callback/credentials',
  '/auth/signin'
];

console.log(`🧪 Testing Auth Endpoints on ${baseUrl}`);
console.log('=' .repeat(50));

async function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const url = new URL(endpoint, baseUrl);
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isDevelopment ? 3001 : 443),
      path: url.pathname,
      method: 'GET',
      headers: {
        'User-Agent': 'Auth-Test-Script/1.0'
      },
      // For development HTTPS with self-signed certificates
      rejectUnauthorized: !isDevelopment
    };

    const req = protocol.request(options, (res) => {
      const rateLimitLimit = res.headers['x-ratelimit-limit'];
      const rateLimitRemaining = res.headers['x-ratelimit-remaining'];
      
      resolve({
        endpoint,
        status: res.statusCode,
        statusText: res.statusMessage,
        rateLimitLimit,
        rateLimitRemaining,
        success: res.statusCode !== 429
      });
    });

    req.on('error', (error) => {
      resolve({
        endpoint,
        status: 'ERROR',
        statusText: error.message,
        rateLimitLimit: 'N/A',
        rateLimitRemaining: 'N/A',
        success: false
      });
    });

    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        endpoint,
        status: 'TIMEOUT',
        statusText: 'Request timeout',
        rateLimitLimit: 'N/A',
        rateLimitRemaining: 'N/A',
        success: false
      });
    });

    req.end();
  });
}

async function runTests() {
  const results = [];
  
  for (const endpoint of endpoints) {
    console.log(`Testing ${endpoint}...`);
    const result = await testEndpoint(endpoint);
    results.push(result);
    
    // Add delay between requests to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log('\n📊 Test Results:');
  console.log('=' .repeat(80));
  console.log('Endpoint'.padEnd(30) + 'Status'.padEnd(10) + 'Rate Limit'.padEnd(15) + 'Remaining'.padEnd(15) + 'Result');
  console.log('-'.repeat(80));

  let allPassed = true;
  
  for (const result of results) {
    const status = result.status.toString().padEnd(10);
    const rateLimit = (result.rateLimitLimit || 'N/A').toString().padEnd(15);
    const remaining = (result.rateLimitRemaining || 'N/A').toString().padEnd(15);
    const resultText = result.success ? '✅ PASS' : '❌ FAIL';
    
    console.log(result.endpoint.padEnd(30) + status + rateLimit + remaining + resultText);
    
    if (!result.success) {
      allPassed = false;
    }
  }

  console.log('\n🎯 Summary:');
  console.log('=' .repeat(50));
  
  if (allPassed) {
    console.log('✅ All auth endpoints are working correctly!');
    console.log('✅ No 429 rate limiting errors detected');
    console.log('✅ Authentication system is ready for use');
  } else {
    console.log('❌ Some auth endpoints failed');
    console.log('❌ Check the results above for details');
    console.log('❌ Review middleware.js and auth.js configuration');
  }

  // Check for essential endpoints specifically
  const essentialEndpoints = ['/api/auth/signin', '/api/auth/session', '/api/auth/providers', '/api/auth/csrf'];
  const essentialResults = results.filter(r => essentialEndpoints.includes(r.endpoint));
  const essentialPassed = essentialResults.every(r => r.success);

  console.log('\n🔑 Essential Auth Endpoints:');
  if (essentialPassed) {
    console.log('✅ All essential auth endpoints are working');
    console.log('✅ Login functionality should work properly');
  } else {
    console.log('❌ Some essential auth endpoints failed');
    console.log('❌ Users may experience login issues');
  }

  // Rate limiting analysis
  console.log('\n📈 Rate Limiting Analysis:');
  const unlimitedEndpoints = results.filter(r => r.rateLimitLimit === 'unlimited');
  const limitedEndpoints = results.filter(r => r.rateLimitLimit !== 'unlimited' && r.rateLimitLimit !== 'N/A');

  console.log(`✅ Unlimited endpoints: ${unlimitedEndpoints.length}`);
  console.log(`⚠️  Limited endpoints: ${limitedEndpoints.length}`);

  if (unlimitedEndpoints.length > 0) {
    console.log('\nUnlimited endpoints (should include essential auth routes):');
    unlimitedEndpoints.forEach(r => console.log(`  - ${r.endpoint}`));
  }

  if (limitedEndpoints.length > 0) {
    console.log('\nLimited endpoints:');
    limitedEndpoints.forEach(r => console.log(`  - ${r.endpoint} (${r.rateLimitLimit} requests)`));
  }

  process.exit(allPassed ? 0 : 1);
}

// Handle script execution
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testEndpoint, runTests };
