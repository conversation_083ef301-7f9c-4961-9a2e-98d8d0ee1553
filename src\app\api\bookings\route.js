import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
import { Package } from '@/models/Package';
import { User } from '@/models/User';
import { scheduleBookingConfirmationEmail } from '@/lib/email-scheduler';

// GET /api/bookings - Get all bookings (no authentication required)
export async function GET(request) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);

    const status = searchParams.get('status');
    const customerId = searchParams.get('customer');
    const packageId = searchParams.get('package');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-createdAt';

    // Build query - no authentication restrictions
    const query = {};
    
    // Apply filters
    if (status) {
      query.status = status;
    }
    
    if (customerId && ['manager', 'admin'].includes(session?.user?.role)) {
      query.customer = customerId;
    }
    
    if (packageId) {
      query.package = packageId;
    }
    
    if (startDate || endDate) {
      query['dates.checkIn'] = {};
      if (startDate) {
        query['dates.checkIn'].$gte = new Date(startDate);
      }
      if (endDate) {
        query['dates.checkIn'].$lte = new Date(endDate);
      }
    }
    
    // Execute query with pagination
    const skip = (page - 1) * limit;
    const bookings = await Booking.find(query)
      .populate('customer', 'name email phone')
      .populate('package', 'name slug category pricing')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count for pagination
    const total = await Booking.countDocuments(query);
    
    return NextResponse.json({
      success: true,
      data: bookings,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching bookings:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch bookings',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// Helper function to create or update guest user
async function createOrUpdateGuestUser(guestInfo) {
  try {
    // Check if user already exists by email
    let existingUser = await User.findOne({ email: guestInfo.email });

    if (existingUser) {
      // Update existing user with new information
      const updateData = {};

      if (guestInfo.firstname && guestInfo.surname) {
        updateData.firstname = guestInfo.firstname;
        updateData.surname = guestInfo.surname;
        updateData.name = `${guestInfo.firstname} ${guestInfo.surname}`;
      } else if (guestInfo.name) {
        updateData.name = guestInfo.name;
        const nameParts = guestInfo.name.trim().split(' ');
        if (nameParts.length >= 2) {
          updateData.firstname = nameParts[0];
          updateData.surname = nameParts.slice(1).join(' ');
        } else {
          updateData.firstname = nameParts[0];
          updateData.surname = '';
        }
      }

      if (guestInfo.phone) updateData.phone = guestInfo.phone;
      if (guestInfo.address) updateData.address = guestInfo.address;
      if (guestInfo.emergencyContact) updateData.emergencyContact = guestInfo.emergencyContact;

      const updatedUser = await User.findByIdAndUpdate(
        existingUser._id,
        updateData,
        { new: true, runValidators: true }
      );

      return updatedUser;
    } else {
      // Create new guest user
      return await User.createGuestUser(guestInfo);
    }
  } catch (error) {
    console.error('Error creating/updating guest user:', error);
    throw error;
  }
}

// POST /api/bookings - Create new booking (public - supports guest bookings)
export async function POST(request) {
  try {
    await connectDB();

    const body = await request.json();
    console.log('Booking request body:', JSON.stringify(body, null, 2));
    
    // Validate required fields - simplified for guest bookings
    const requiredFields = ['packageId', 'checkIn', 'checkOut'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `${field} is required`,
            receivedData: body,
          },
          { status: 400 }
        );
      }
    }

    // Validate guest information
    if (!body.guestInfo || !body.guestInfo.email || !body.guestInfo.firstname || !body.guestInfo.surname) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'Guest information (firstname, surname, and email) is required',
          receivedData: body,
        },
        { status: 400 }
      );
    }
    
    // Get package details
    const packageData = await Package.findById(body.packageId);
    if (!packageData) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Package not found',
        },
        { status: 404 }
      );
    }

    // Check package availability
    const checkIn = new Date(body.checkIn);
    const checkOut = new Date(body.checkOut);

    if (!packageData.isAvailableForDates(checkIn, checkOut)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unavailable',
          message: 'Package is not available for selected dates',
        },
        { status: 409 }
      );
    }

    // Guest count is collected for informational purposes only (no capacity validation)
    const totalGuests = body.guests?.adults || 1;
    console.log(`Booking for ${totalGuests} guests in ${packageData.name} package`);

    let customerId;

    // Create guest user from guestInfo data (no authentication required)
    const guestUser = await createOrUpdateGuestUser(body.guestInfo);
    customerId = guestUser._id;
    
    // Calculate pricing
    const guestType = body.guests?.guestType || 'individuals';
    const basePrice = packageData.getPrice();
    const duration = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));

    const pricing = {
      basePrice: basePrice,
      taxes: Math.round(basePrice * 0.1), // 10% tax
      fees: 0,
      discounts: 0,
      totalAmount: basePrice + Math.round(basePrice * 0.1),
      currency: 'USD',
    };

    // Create booking
    const bookingData = {
      customer: customerId,
      package: body.packageId,
      dates: {
        checkIn,
        checkOut,
        duration,
      },
      guests: {
        adults: totalGuests,
        children: body.guests?.children || 0,
        total: totalGuests,
        guestType,
      },
      guestDetails: body.guestDetails || [body.guestInfo],
      pricing,
      specialRequests: body.specialRequests || {},
      source: body.source || 'website',
      metadata: {
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    };
    
    const booking = new Booking(bookingData);
    await booking.save();

    // Populate the booking for response
    await booking.populate('customer', 'firstname surname name email phone');
    await booking.populate('package', 'name slug category');

    // Send booking confirmation email
    try {
      await scheduleBookingConfirmationEmail(booking._id);
      console.log(`Booking confirmation email scheduled for ${booking.bookingNumber}`);
    } catch (emailError) {
      console.error('Failed to send booking confirmation email:', emailError);
      // Don't fail the booking creation if email fails
    }

    return NextResponse.json(
      {
        success: true,
        data: booking,
        message: 'Booking created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating booking:', error);
    console.error('Error stack:', error.stack);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    if (error.name === 'CastError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid Data',
          message: 'Invalid package ID or data format',
          details: error.message,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create booking',
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      },
      { status: 500 }
    );
  }
}

// PUT /api/bookings - Bulk update bookings (no authentication required)
export async function PUT(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    const { bookings } = body;
    
    if (!Array.isArray(bookings)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'bookings must be an array',
        },
        { status: 400 }
      );
    }
    
    const results = [];
    
    for (const bookingData of bookings) {
      try {
        const { _id, ...updateData } = bookingData;
        const updatedBooking = await Booking.findByIdAndUpdate(
          _id,
          updateData,
          { new: true, runValidators: true }
        ).populate('customer', 'name email phone')
         .populate('package', 'name slug category');
        
        if (updatedBooking) {
          results.push({
            id: _id,
            success: true,
            data: updatedBooking,
          });
        } else {
          results.push({
            id: _id,
            success: false,
            error: 'Booking not found',
          });
        }
      } catch (error) {
        results.push({
          id: bookingData._id,
          success: false,
          error: error.message,
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed',
    });
  } catch (error) {
    console.error('Error bulk updating bookings:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update bookings',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
