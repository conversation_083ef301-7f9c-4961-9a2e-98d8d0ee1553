import { NextResponse } from 'next/server';

// Rate limiting storage
const rateLimitMap = new Map();

// CLEANUP DISABLED - No longer needed since rate limiting is disabled
// setInterval(() => {
//   const now = Date.now();
//   for (const [key, data] of rateLimitMap.entries()) {
//     if (now - data.resetTime > 15 * 60 * 1000) {
//       rateLimitMap.delete(key);
//     }
//   }
// }, 15 * 60 * 1000);

// Rate limiting function - DISABLED to prevent rate limit errors
function rateLimit(ip, limit = 1000, windowMs = 15 * 60 * 1000) {
  // RATE LIMITING DISABLED - Always allow requests
  return {
    allowed: true,
    remaining: 999,
    resetTime: Date.now() + windowMs
  };

  // Original rate limiting code commented out to prevent 429 errors:
  // const now = Date.now();
  // const key = `${ip}`;
  //
  // if (!rateLimitMap.has(key)) {
  //   rateLimitMap.set(key, {
  //     count: 1,
  //     resetTime: now + windowMs,
  //   });
  //   return { allowed: true, remaining: limit - 1 };
  // }
  //
  // const data = rateLimitMap.get(key);
  //
  // if (now > data.resetTime) {
  //   // Reset the window
  //   data.count = 1;
  //   data.resetTime = now + windowMs;
  //   return { allowed: true, remaining: limit - 1 };
  // }
  //
  // if (data.count >= limit) {
  //   return {
  //     allowed: false,
  //     remaining: 0,
  //     resetTime: data.resetTime
  //   };
  // }
  //
  // data.count++;
  // return {
  //   allowed: true,
  //   remaining: limit - data.count
  // };
}

// AUTHENTICATION REMOVED - All routes are now public

export default async function middleware(request) {
  const { pathname } = request.nextUrl;

  // AUTHENTICATION AND RATE LIMITING COMPLETELY DISABLED
  // All routes are now public and unlimited

  // Create response with basic headers
  const response = NextResponse.next();

  // Add CORS headers for static assets
  if (pathname.startsWith('/assets/')) {
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type');
  }

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // All routes are public - no authentication required
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
