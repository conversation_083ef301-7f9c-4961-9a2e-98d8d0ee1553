# 360° Marker Data Persistence Fix

## Issues Identified and Fixed

### 1. MongoDB Schema Validation Issue ✅
**Problem**: The `markerList` field was defined as `{type:Array}` which provided no validation
**Fix**: Added proper schema validation with MarkerSchema subdocument

```javascript
// Before: markerList:{type:Array}
// After: markerList:{type:[MarkerSchema],default:[]}

const MarkerSchema = new Schema({
    name: { type: String, required: true },
    markerType: { 
        type: String, 
        required: true,
        enum: ['landingPage', 'guide', 'upstairs', 'downstairs', 'infoVideo', 'infoDoc', 'infoImage']
    },
    x: { type: Number, default: 0 },
    y: { type: Number, default: 0 },
    z: { type: Number, default: 0 },
    _360Name: { type: String, default: '' }, // Navigation markers
    id: { type: String, default: '' }        // Content markers
}, { _id: false });
```

### 2. State Synchronization Race Condition ✅
**Problem**: Local `markerList` state in MarkersInputList wasn't capturing Leva control position updates
**Fix**: Modified submission to use parent state (`_360Object.markerList`) instead of local state

```javascript
// CRITICAL FIX: Use _360Object.markerList instead of local markerList
const finalMarkerList = _360Object.markerList || markerList || []

const payload = {
    cameraPosition: _360Object.cameraPosition,
    _360Rotation: _360Object._360Rotation,
    markerList: finalMarkerList // Use most current data from parent state
}
```

### 3. Reversed Synchronization Logic ✅
**Problem**: Component was trying to sync FROM local TO parent instead of FROM parent TO local
**Fix**: Corrected the synchronization direction to capture Leva control updates

```javascript
// FIXED: Sync FROM parent TO local state to capture Leva control updates
useEffect(() => {
    const parentMarkerList = _360Object.markerList || []
    
    // Check if parent has position updates that local state doesn't have
    const hasParentUpdates = parentMarkerList.some((parentMarker, index) => {
        const localMarker = markerList[index]
        return parentMarker.name === localMarker.name &&
               (parentMarker.x !== localMarker.x ||
                parentMarker.y !== localMarker.y ||
                parentMarker.z !== localMarker.z)
    })

    if (hasParentUpdates && parentMarkerList.length === markerList.length) {
        setMarkerList([...parentMarkerList]) // Sync local state with parent
    }
}, [_360Object.markerList, markerList, current360Id])
```

### 4. Enhanced API Validation and Debugging ✅
**Problem**: No validation or debugging for marker data in API routes
**Fix**: Added comprehensive logging and data validation

```javascript
// Validate marker data if present
if (body.markerList && Array.isArray(body.markerList)) {
    body.markerList = body.markerList.map(marker => ({
        name: marker.name || '',
        markerType: marker.markerType || '',
        x: Number(marker.x) || 0,
        y: Number(marker.y) || 0,
        z: Number(marker.z) || 0,
        _360Name: marker._360Name || '',
        id: marker.id || ''
    }));
}
```

### 5. Post-Submission Data Refresh ✅
**Problem**: No mechanism to ensure UI reflects saved database state
**Fix**: Added comprehensive refresh mechanism after successful submission

```javascript
// Update local state to match the database response
setMarkerList(result.data.markerList || [])

// Trigger dashboard refresh to ensure full synchronization
setTimeout(() => {
    window.refreshDashboardData()
}, 100)
```

## Testing Instructions

### Test 1: Marker Position Persistence
1. Open 360° Viewer Dashboard
2. Select any 360° image
3. Add a new marker using the input form
4. Use Leva controls to move the marker to a specific position (e.g., x: 5, y: 3, z: -2)
5. Click "Save" button
6. Refresh the page completely
7. **Expected**: Marker should appear at the exact same position

### Test 2: Multiple Marker Management
1. Add 3-4 markers with different types
2. Position each marker using Leva controls
3. Update marker properties (select different content for content markers)
4. Save changes
5. Switch to a different 360° image and back
6. **Expected**: All markers should maintain their positions and properties

### Test 3: Real-time Synchronization
1. Add a marker
2. Move it using Leva controls (watch the position values change)
3. Immediately click Save (without waiting)
4. **Expected**: The saved position should match the Leva control values

### Test 4: Error Handling
1. Try to add a marker with duplicate name
2. Try to save with invalid data
3. **Expected**: Appropriate error messages should appear

## Data Flow Verification

### Console Logging
The fix includes comprehensive console logging to track data flow:

```javascript
// In MarkersInputList.jsx
console.log('Submitting payload with final marker list:', {
    localMarkerCount: markerList.length,
    parentMarkerCount: _360Object.markerList?.length || 0,
    finalMarkerCount: finalMarkerList.length,
    payload
});

// In API route
console.log('360° Update Request:', {
    id,
    hasMarkerList: !!body.markerList,
    markerCount: body.markerList?.length || 0,
    markers: body.markerList?.map(m => ({ name: m.name, type: m.markerType, x: m.x, y: m.y, z: m.z }))
});
```

### Debugging Steps
1. Open browser developer tools
2. Go to Console tab
3. Perform marker operations
4. Watch for log messages showing data flow
5. Verify that submitted data matches saved data

## Files Modified

1. **src/models/_360Model.js** - Enhanced schema validation
2. **src/components/360s/MarkersInputList.jsx** - Fixed state synchronization
3. **src/app/api/360s/[id]/route.js** - Added validation and debugging

## Git Commit Message
```
fix: resolve 360° marker data persistence issues

- Add proper MongoDB schema validation for markerList
- Fix state synchronization race condition between Leva controls and submission
- Correct synchronization direction from parent to local state
- Add comprehensive API validation and debugging
- Implement post-submission data refresh mechanism
- Ensure marker position updates from Leva controls are captured in database
```

## Expected Behavior After Fix

1. **Immediate UI Updates**: Marker changes appear instantly in the UI
2. **Persistent Storage**: All marker data persists correctly after page reload
3. **Position Accuracy**: Leva control positions are accurately saved to database
4. **Synchronization**: No data loss between UI state and database state
5. **Error Handling**: Clear feedback for validation errors and save failures
