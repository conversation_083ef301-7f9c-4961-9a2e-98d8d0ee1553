'use client';

import { useState, useEffect } from 'react';

export default function BookingFilters({ filters, onFilterChange, onRefresh }) {
  const [localFilters, setLocalFilters] = useState(filters);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (key, value) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      search: '',
      status: '',
      paymentStatus: '',
      package: '',
      dateRange: '',
      sort: '-createdAt',
    };
    setLocalFilters(clearedFilters);
    onFilterChange(clearedFilters);
  };

  const hasActiveFilters = Object.values(localFilters).some(value => 
    value && value !== '-createdAt'
  );

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Filters</h3>
        <div className="flex items-center space-x-2">
          {hasActiveFilters && (
            <button
              onClick={handleClearFilters}
              className="text-sm text-gray-500 hover:text-gray-700 underline"
            >
              Clear All
            </button>
          )}
          <button
            onClick={onRefresh}
            className="p-2 text-gray-400 hover:text-gray-600"
            title="Refresh"
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-2 text-gray-400 hover:text-gray-600"
            title={isExpanded ? "Collapse" : "Expand"}
          >
            <svg 
              className={`h-4 w-4 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Always visible filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {/* Search */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Search
          </label>
          <input
            type="text"
            placeholder="Booking number, guest name..."
            value={localFilters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Booking Status
          </label>
          <select
            value={localFilters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="checked_in">Checked In</option>
            <option value="checked_out">Checked Out</option>
            <option value="cancelled">Cancelled</option>
            <option value="no_show">No Show</option>
          </select>
        </div>

        {/* Payment Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Payment Status
          </label>
          <select
            value={localFilters.paymentStatus}
            onChange={(e) => handleFilterChange('paymentStatus', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Payment Statuses</option>
            <option value="pending">Pending</option>
            <option value="partial">Partial</option>
            <option value="paid">Paid</option>
            <option value="failed">Failed</option>
            <option value="refunded">Refunded</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        {/* Sort */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Sort By
          </label>
          <select
            value={localFilters.sort}
            onChange={(e) => handleFilterChange('sort', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="-createdAt">Newest First</option>
            <option value="createdAt">Oldest First</option>
            <option value="dates.checkIn">Check-in Date (Earliest)</option>
            <option value="-dates.checkIn">Check-in Date (Latest)</option>
            <option value="dates.checkOut">Check-out Date (Earliest)</option>
            <option value="-dates.checkOut">Check-out Date (Latest)</option>
            <option value="customer.name">Guest Name A-Z</option>
            <option value="-customer.name">Guest Name Z-A</option>
            <option value="-pricing.totalAmount">Amount (High to Low)</option>
            <option value="pricing.totalAmount">Amount (Low to High)</option>
          </select>
        </div>
      </div>

      {/* Expandable advanced filters */}
      {isExpanded && (
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Advanced Filters</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date Range
              </label>
              <select
                value={localFilters.dateRange}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Dates</option>
                <option value="today">Today</option>
                <option value="tomorrow">Tomorrow</option>
                <option value="this_week">This Week</option>
                <option value="next_week">Next Week</option>
                <option value="this_month">This Month</option>
                <option value="next_month">Next Month</option>
                <option value="past_due">Past Due</option>
              </select>
            </div>

            {/* Package Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Package
              </label>
              <input
                type="text"
                placeholder="Package name..."
                value={localFilters.package}
                onChange={(e) => handleFilterChange('package', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Guest Count */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Guest Count
              </label>
              <select
                value={localFilters.guestCount}
                onChange={(e) => handleFilterChange('guestCount', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Guest Counts</option>
                <option value="1">1 Guest</option>
                <option value="2">2 Guests</option>
                <option value="3-4">3-4 Guests</option>
                <option value="5+">5+ Guests</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Active filters display */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t">
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-gray-500">Active filters:</span>
            {Object.entries(localFilters).map(([key, value]) => {
              if (!value || value === '-createdAt') return null;
              
              let displayValue = value;
              if (key === 'sort') {
                const sortLabels = {
                  '-createdAt': 'Newest First',
                  'createdAt': 'Oldest First',
                  'dates.checkIn': 'Check-in (Earliest)',
                  '-dates.checkIn': 'Check-in (Latest)',
                  'dates.checkOut': 'Check-out (Earliest)',
                  '-dates.checkOut': 'Check-out (Latest)',
                  'customer.name': 'Guest Name A-Z',
                  '-customer.name': 'Guest Name Z-A',
                  '-pricing.totalAmount': 'Amount (High to Low)',
                  'pricing.totalAmount': 'Amount (Low to High)',
                };
                displayValue = sortLabels[value] || value;
              } else if (key === 'status' || key === 'paymentStatus') {
                displayValue = value.charAt(0).toUpperCase() + value.slice(1).replace('_', ' ');
              } else if (key === 'dateRange') {
                const rangeLabels = {
                  today: 'Today',
                  tomorrow: 'Tomorrow',
                  this_week: 'This Week',
                  next_week: 'Next Week',
                  this_month: 'This Month',
                  next_month: 'Next Month',
                  past_due: 'Past Due',
                };
                displayValue = rangeLabels[value] || value;
              }
              
              return (
                <span
                  key={key}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {key}: {displayValue}
                  <button
                    onClick={() => handleFilterChange(key, key === 'sort' ? '-createdAt' : '')}
                    className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600"
                  >
                    <svg className="w-2 h-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                      <path strokeLinecap="round" strokeWidth="1.5" d="m1 1 6 6m0-6L1 7" />
                    </svg>
                  </button>
                </span>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
