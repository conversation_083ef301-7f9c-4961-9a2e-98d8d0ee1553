/**
 * Production-ready asset loading utilities for 360° images
 * Handles Firebase Storage URLs, local assets, and fallback mechanisms
 */

/**
 * Validate if a URL is accessible
 * @param {string} url - URL to validate
 * @returns {Promise<boolean>} - Whether the URL is accessible
 */
export async function validateAssetUrl(url) {
  if (!url || typeof url !== 'string') {
    return false;
  }

  try {
    // For production, we can do a HEAD request to check if the asset exists
    const response = await fetch(url, {
      method: 'HEAD',
      mode: 'cors',
      cache: 'no-cache'
    });

    const contentType = response.headers.get('content-type');
    return response.ok && (
      contentType?.startsWith('image/') ||
      contentType?.startsWith('video/') ||
      contentType?.startsWith('audio/') ||
      contentType?.startsWith('application/') ||
      contentType?.startsWith('text/') ||
      contentType?.startsWith('font/')
    );
  } catch (error) {
    console.warn(`URL validation failed for ${url}:`, error);
    return false;
  }
}

/**
 * Legacy function for backward compatibility
 * @param {string} url - URL to validate
 * @returns {Promise<boolean>} - Whether the URL is accessible
 */
export async function validateImageUrl(url) {
  return validateAssetUrl(url);
}

/**
 * Normalize and validate asset URLs for production
 * @param {string} url - Original URL from database
 * @param {string} id - Asset ID for fallback generation
 * @param {string} type - Asset type ('image', 'video', 'audio', 'font', 'general')
 * @returns {Promise<string|null>} - Validated URL or null if not accessible
 */
export async function resolveAssetUrl(url, id, type = 'general') {
  if (!url) {
    console.error(`No URL provided for asset ID: ${id}`);
    return null;
  }

  // Normalize the URL
  const normalizedUrl = normalizeUrl(url);

  // Check if the primary URL is accessible
  const isValid = await validateAssetUrl(normalizedUrl);
  if (isValid) {
    return normalizedUrl;
  }

  console.warn(`Primary URL failed validation for ID ${id}: ${normalizedUrl}`);

  // Try fallback URLs based on asset type
  const fallbackUrls = generateFallbackUrls(id, url, type);

  for (const fallbackUrl of fallbackUrls) {
    const isValidFallback = await validateAssetUrl(fallbackUrl);
    if (isValidFallback) {
      console.log(`Using fallback URL for ID ${id}: ${fallbackUrl}`);
      return fallbackUrl;
    }
  }

  console.error(`All URLs failed for asset ID ${id}`);
  return null;
}

/**
 * Legacy function for backward compatibility
 * @param {string} url - Original URL from database
 * @param {string} id - Image ID for fallback generation
 * @returns {Promise<string|null>} - Validated URL or null if not accessible
 */
export async function resolveImageUrl(url, id) {
  return resolveAssetUrl(url, id, 'image');
}

/**
 * Normalize URL for consistent handling
 * @param {string} url - Original URL
 * @returns {string} - Normalized URL
 */
function normalizeUrl(url) {
  if (!url) return '';

  // If it's already a full URL (Firebase, CDN, etc.), return as-is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // Handle local paths
  if (url.startsWith('/uploads/') || url.startsWith('/assets/')) {
    return url;
  }

  // If it's a relative path without leading slash, add it
  if (!url.startsWith('/')) {
    return `/${url}`;
  }

  return url;
}

/**
 * Generate fallback URLs for assets based on type
 * @param {string} id - Asset ID
 * @param {string} originalUrl - Original URL that failed
 * @param {string} type - Asset type ('image', 'video', 'audio', 'font', 'general')
 * @returns {string[]} - Array of fallback URLs to try
 */
function generateFallbackUrls(id, originalUrl, type = 'general') {
  const fallbacks = [];

  // Extract filename from original URL if possible
  const filename = extractFilename(originalUrl);

  // Type-specific fallback generation
  switch (type) {
    case 'image':
      if (filename) {
        fallbacks.push(`/uploads/360s/${filename}`);
        fallbacks.push(`/assets/360s/${filename}`);
        fallbacks.push(`/assets/images/${filename}`);
        fallbacks.push(`/assets/${filename}`);
      }

      // Try with ID as filename with image extensions
      const imageExts = ['jpg', 'jpeg', 'png', 'webp', 'svg'];
      for (const ext of imageExts) {
        fallbacks.push(`/uploads/360s/${id}.${ext}`);
        fallbacks.push(`/assets/360s/${id}.${ext}`);
        fallbacks.push(`/assets/images/${id}.${ext}`);
        fallbacks.push(`/assets/${id}.${ext}`);
      }

      // Default fallback images
      fallbacks.push('/assets/360s/default-panorama.jpg');
      fallbacks.push('/assets/images/360-placeholder.jpg');
      fallbacks.push('/assets/placeholder.png');
      break;

    case 'video':
      if (filename) {
        fallbacks.push(`/uploads/videos/${filename}`);
        fallbacks.push(`/assets/videos/${filename}`);
        fallbacks.push(`/assets/${filename}`);
      }

      const videoExts = ['mp4', 'webm', 'ogg'];
      for (const ext of videoExts) {
        fallbacks.push(`/uploads/videos/${id}.${ext}`);
        fallbacks.push(`/assets/videos/${id}.${ext}`);
        fallbacks.push(`/assets/${id}.${ext}`);
      }

      fallbacks.push('/assets/videos/placeholder.mp4');
      break;

    case 'audio':
      if (filename) {
        fallbacks.push(`/uploads/audio/${filename}`);
        fallbacks.push(`/assets/audio/${filename}`);
        fallbacks.push(`/assets/${filename}`);
      }

      const audioExts = ['mp3', 'wav', 'ogg'];
      for (const ext of audioExts) {
        fallbacks.push(`/uploads/audio/${id}.${ext}`);
        fallbacks.push(`/assets/audio/${id}.${ext}`);
        fallbacks.push(`/assets/${id}.${ext}`);
      }
      break;

    case 'font':
      if (filename) {
        fallbacks.push(`/assets/fonts/${filename}`);
        fallbacks.push(`/assets/${filename}`);
      }

      const fontExts = ['woff2', 'woff', 'ttf', 'otf'];
      for (const ext of fontExts) {
        fallbacks.push(`/assets/fonts/${id}.${ext}`);
        fallbacks.push(`/assets/${id}.${ext}`);
      }
      break;

    default: // general
      if (filename) {
        fallbacks.push(`/assets/${filename}`);
        fallbacks.push(`/assets/images/${filename}`);
        fallbacks.push(`/assets/ui/${filename}`);
        fallbacks.push(`/uploads/${filename}`);
      }

      // Try common extensions
      const commonExts = ['png', 'jpg', 'jpeg', 'svg', 'webp'];
      for (const ext of commonExts) {
        fallbacks.push(`/assets/${id}.${ext}`);
        fallbacks.push(`/assets/images/${id}.${ext}`);
        fallbacks.push(`/assets/ui/${id}.${ext}`);
      }

      fallbacks.push('/assets/placeholder.png');
      fallbacks.push('/assets/default.png');
      break;
  }

  return fallbacks;
}

/**
 * Extract filename from URL
 * @param {string} url - URL to extract filename from
 * @returns {string|null} - Extracted filename or null
 */
function extractFilename(url) {
  if (!url) return null;

  try {
    // Handle Firebase URLs
    if (url.includes('firebase')) {
      const match = url.match(/\/([^\/\?]+)\?/);
      if (match) return decodeURIComponent(match[1]);
    }

    // Handle regular URLs
    const urlObj = new URL(url, window.location.origin);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop();
    
    return filename && filename.includes('.') ? filename : null;
  } catch (error) {
    // If URL parsing fails, try simple string extraction
    const parts = url.split('/');
    const lastPart = parts[parts.length - 1];
    
    // Remove query parameters
    const filename = lastPart.split('?')[0];
    
    return filename && filename.includes('.') ? filename : null;
  }
}

/**
 * Enhanced image preloader with production error handling
 * @param {string} url - Image URL to preload
 * @param {Object} options - Loading options
 * @returns {Promise<HTMLImageElement>} - Loaded image element
 */
export async function preloadImageWithFallback(url, options = {}) {
  const {
    timeout = 15000,
    retries = 2,
    id = 'unknown'
  } = options;

  // First, resolve the best URL
  const resolvedUrl = await resolveImageUrl(url, id);
  
  if (!resolvedUrl) {
    throw new Error(`No valid URL found for image ID: ${id}`);
  }

  // Load the image with retries
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const image = await loadImageWithTimeout(resolvedUrl, timeout);
      console.log(`Successfully preloaded image for ID ${id} on attempt ${attempt + 1}`);
      return image;
    } catch (error) {
      console.warn(`Preload attempt ${attempt + 1} failed for ID ${id}:`, error);
      
      if (attempt === retries) {
        throw new Error(`Failed to preload image after ${retries + 1} attempts: ${error.message}`);
      }
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
    }
  }
}

/**
 * Load image with timeout
 * @param {string} url - Image URL
 * @param {number} timeout - Timeout in milliseconds
 * @returns {Promise<HTMLImageElement>} - Loaded image
 */
function loadImageWithTimeout(url, timeout) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    const timeoutId = setTimeout(() => {
      img.onload = null;
      img.onerror = null;
      reject(new Error(`Image loading timeout: ${url}`));
    }, timeout);

    img.onload = () => {
      clearTimeout(timeoutId);
      resolve(img);
    };

    img.onerror = () => {
      clearTimeout(timeoutId);
      reject(new Error(`Image loading error: ${url}`));
    };

    // Set cross-origin for CORS handling
    img.crossOrigin = 'anonymous';
    img.src = url;
  });
}

/**
 * Check if we're in production environment
 * @returns {boolean} - Whether we're in production
 */
export function isProduction() {
  return process.env.NODE_ENV === 'production';
}

/**
 * Get the appropriate base URL for the current environment
 * @returns {string} - Base URL
 */
export function getBaseUrl() {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  
  return isProduction() 
    ? 'https://victorchelemu.com' 
    : 'http://localhost:3001';
}
