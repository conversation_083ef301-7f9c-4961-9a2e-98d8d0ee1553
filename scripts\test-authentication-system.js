#!/usr/bin/env node

/**
 * Authentication System Testing Script
 * 
 * This script tests the complete authentication flow
 */

const https = require('https');
const crypto = require('crypto');

// Configuration
const isDevelopment = process.env.NODE_ENV !== 'production';
const baseUrl = isDevelopment ? 'https://localhost:3001' : 'https://victorchelemu.com';

console.log(`🔐 Testing Authentication System on ${baseUrl}`);
console.log('=' .repeat(60));

// Test user data
const testUsers = [
  {
    firstname: 'Test',
    surname: 'User',
    email: `test.user.${Date.now()}@example.com`,
    password: 'testpassword123',
    confirmPassword: 'testpassword123',
    acceptTerms: true
  },
  {
    firstname: 'Admin',
    surname: 'Test',
    email: '<EMAIL>', // Should get admin role
    password: 'adminpassword123',
    confirmPassword: 'adminpassword123',
    acceptTerms: true
  }
];

async function makeRequest(endpoint, method = 'GET', body = null, headers = {}) {
  return new Promise((resolve) => {
    const url = new URL(endpoint, baseUrl);
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isDevelopment ? 3001 : 443),
      path: url.pathname + url.search,
      method: method,
      headers: {
        'User-Agent': 'Auth-Test-Script/1.0',
        'Content-Type': 'application/json',
        ...headers
      },
      rejectUnauthorized: !isDevelopment
    };

    if (body) {
      const bodyString = JSON.stringify(body);
      options.headers['Content-Length'] = Buffer.byteLength(bodyString);
    }

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = data ? JSON.parse(data) : {};
          resolve({
            endpoint,
            method,
            status: res.statusCode,
            statusText: res.statusMessage,
            headers: res.headers,
            data: parsedData,
            success: res.statusCode >= 200 && res.statusCode < 300
          });
        } catch (parseError) {
          resolve({
            endpoint,
            method,
            status: res.statusCode,
            statusText: res.statusMessage,
            headers: res.headers,
            data: data,
            parseError: parseError.message,
            success: false
          });
        }
      });
    });

    req.on('error', (error) => {
      resolve({
        endpoint,
        method,
        status: 'ERROR',
        statusText: error.message,
        success: false
      });
    });

    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        endpoint,
        method,
        status: 'TIMEOUT',
        statusText: 'Request timeout',
        success: false
      });
    });

    if (body) {
      req.write(JSON.stringify(body));
    }

    req.end();
  });
}

async function testAuthPages() {
  console.log('\n📄 Testing Authentication Pages...');
  console.log('-'.repeat(40));
  
  const pages = [
    '/auth/signin',
    '/auth/signup',
    '/auth/forgot-password'
  ];
  
  const results = {};
  
  for (const page of pages) {
    console.log(`Testing ${page}...`);
    const result = await makeRequest(page);
    results[page] = result.success;
    console.log(`  ${page}: ${result.success ? '✅ ACCESSIBLE' : '❌ FAILED'} (${result.status})`);
  }
  
  return results;
}

async function testSignupFlow() {
  console.log('\n📝 Testing User Registration Flow...');
  console.log('-'.repeat(40));
  
  const results = {};
  
  for (let i = 0; i < testUsers.length; i++) {
    const user = testUsers[i];
    console.log(`\nTesting registration for ${user.email}...`);
    
    // Test email availability check
    console.log('  1. Checking email availability...');
    const emailCheck = await makeRequest(`/api/auth/signup?email=${encodeURIComponent(user.email)}`);
    console.log(`     Email availability: ${emailCheck.success ? '✅ AVAILABLE' : '❌ FAILED'}`);
    
    // Test user registration
    console.log('  2. Creating user account...');
    const signup = await makeRequest('/api/auth/signup', 'POST', user);
    console.log(`     Registration: ${signup.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    if (signup.success) {
      console.log(`     Role assigned: ${signup.data?.data?.role || 'unknown'}`);
      
      // Verify admin <NAME_EMAIL>
      if (user.email === '<EMAIL>') {
        const isAdmin = signup.data?.data?.role === 'admin';
        console.log(`     Admin role check: ${isAdmin ? '✅ ADMIN ASSIGNED' : '❌ ADMIN NOT ASSIGNED'}`);
        results.adminRoleAssignment = isAdmin;
      }
    } else {
      console.log(`     Error: ${signup.data?.message || signup.statusText}`);
    }
    
    results[`signup_${i}`] = signup.success;
  }
  
  return results;
}

async function testPasswordResetFlow() {
  console.log('\n🔄 Testing Password Reset Flow...');
  console.log('-'.repeat(40));
  
  const testEmail = testUsers[0].email;
  
  // Test forgot password request
  console.log('1. Testing forgot password request...');
  const forgotPassword = await makeRequest('/api/auth/forgot-password', 'POST', {
    email: testEmail
  });
  
  console.log(`   Forgot password: ${forgotPassword.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  if (!forgotPassword.success) {
    console.log(`   Error: ${forgotPassword.data?.message || forgotPassword.statusText}`);
  }
  
  // Test with invalid token (since we can't get real token from email)
  console.log('2. Testing reset password with invalid token...');
  const resetPassword = await makeRequest('/api/auth/reset-password', 'POST', {
    token: 'invalid-token',
    password: 'newpassword123',
    confirmPassword: 'newpassword123'
  });
  
  const expectedFailure = !resetPassword.success && resetPassword.status === 400;
  console.log(`   Invalid token handling: ${expectedFailure ? '✅ CORRECTLY REJECTED' : '❌ UNEXPECTED RESULT'}`);
  
  return {
    forgotPassword: forgotPassword.success,
    invalidTokenHandling: expectedFailure
  };
}

async function testAPIEndpoints() {
  console.log('\n🔌 Testing Authentication API Endpoints...');
  console.log('-'.repeat(40));
  
  const endpoints = [
    { path: '/api/auth/signup', method: 'GET', params: '?email=<EMAIL>' },
    { path: '/api/auth/signup', method: 'POST', expectStatus: 400 }, // Missing data
    { path: '/api/auth/forgot-password', method: 'POST', expectStatus: 400 }, // Missing email
    { path: '/api/auth/reset-password', method: 'GET', params: '?token=invalid', expectStatus: 400 },
    { path: '/api/auth/reset-password', method: 'POST', expectStatus: 400 }, // Missing data
  ];
  
  const results = {};
  
  for (const endpoint of endpoints) {
    const fullPath = endpoint.path + (endpoint.params || '');
    console.log(`Testing ${endpoint.method} ${fullPath}...`);
    
    const result = await makeRequest(fullPath, endpoint.method, endpoint.method === 'POST' ? {} : null);
    
    const expectedStatus = endpoint.expectStatus || 200;
    const statusMatch = result.status === expectedStatus;
    
    console.log(`  Status: ${result.status} (expected: ${expectedStatus}) ${statusMatch ? '✅' : '❌'}`);
    
    results[`${endpoint.method}_${endpoint.path}`] = statusMatch;
  }
  
  return results;
}

async function testEnvironmentConfiguration() {
  console.log('\n⚙️  Testing Environment Configuration...');
  console.log('-'.repeat(40));
  
  const requiredVars = [
    'NEXTAUTH_URL',
    'NEXTAUTH_SECRET',
    'MONGODB_URI',
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'EMAIL_SERVER_HOST',
    'EMAIL_SERVER_USER'
  ];
  
  const results = {};
  
  for (const varName of requiredVars) {
    const isSet = !!process.env[varName];
    console.log(`${varName}: ${isSet ? '✅ SET' : '❌ MISSING'}`);
    results[varName] = isSet;
  }
  
  return results;
}

async function runAllTests() {
  console.log('🚀 Starting Authentication System Tests...\n');
  
  const results = {
    environment: await testEnvironmentConfiguration(),
    pages: await testAuthPages(),
    signup: await testSignupFlow(),
    passwordReset: await testPasswordResetFlow(),
    apiEndpoints: await testAPIEndpoints()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('=' .repeat(60));
  
  // Environment Configuration
  console.log('Environment Configuration:');
  const envPassed = Object.values(results.environment).filter(Boolean).length;
  const envTotal = Object.keys(results.environment).length;
  console.log(`  ${envPassed}/${envTotal} variables configured ${envPassed === envTotal ? '✅' : '❌'}`);
  
  // Authentication Pages
  console.log('\nAuthentication Pages:');
  const pagesPassed = Object.values(results.pages).filter(Boolean).length;
  const pagesTotal = Object.keys(results.pages).length;
  console.log(`  ${pagesPassed}/${pagesTotal} pages accessible ${pagesPassed === pagesTotal ? '✅' : '❌'}`);
  
  // User Registration
  console.log('\nUser Registration:');
  const signupPassed = Object.values(results.signup).filter(Boolean).length;
  const signupTotal = Object.keys(results.signup).length;
  console.log(`  ${signupPassed}/${signupTotal} signup tests passed ${signupPassed === signupTotal ? '✅' : '❌'}`);
  
  if (results.signup.adminRoleAssignment !== undefined) {
    console.log(`  Admin role assignment: ${results.signup.adminRoleAssignment ? '✅' : '❌'}`);
  }
  
  // Password Reset
  console.log('\nPassword Reset:');
  const resetPassed = Object.values(results.passwordReset).filter(Boolean).length;
  const resetTotal = Object.keys(results.passwordReset).length;
  console.log(`  ${resetPassed}/${resetTotal} reset tests passed ${resetPassed === resetTotal ? '✅' : '❌'}`);
  
  // API Endpoints
  console.log('\nAPI Endpoints:');
  const apiPassed = Object.values(results.apiEndpoints).filter(Boolean).length;
  const apiTotal = Object.keys(results.apiEndpoints).length;
  console.log(`  ${apiPassed}/${apiTotal} endpoint tests passed ${apiPassed === apiTotal ? '✅' : '❌'}`);
  
  // Overall Assessment
  const allTestsPassed = envPassed === envTotal && 
                        pagesPassed === pagesTotal && 
                        signupPassed === signupTotal && 
                        resetPassed === resetTotal && 
                        apiPassed === apiTotal;
  
  console.log('\n🎯 Overall Assessment:');
  if (allTestsPassed) {
    console.log('✅ Authentication system is working correctly!');
    console.log('✅ All tests passed successfully');
  } else {
    console.log('❌ Authentication system has issues');
    console.log('❌ Review the failed tests above');
  }
  
  console.log('\n💡 Next Steps:');
  console.log('- Test OAuth providers (Google, Facebook)');
  console.log('- Test sign-in with created accounts');
  console.log('- Test role-based access control');
  console.log('- Test email notifications');
  
  return allTestsPassed;
}

// Handle script execution
if (require.main === module) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { makeRequest, testAuthPages, testSignupFlow, runAllTests };
