import { NextResponse } from 'next/server';
import { uploadFileServer } from '@/lib/server-file-upload';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';

// POST /api/test-360-fixes - Test both Firebase URL and confirmation dialog fixes
export async function POST(request) {
  try {
    console.log('Testing 360° fixes: Firebase URL saving and confirmation dialog...');
    
    const results = {
      firebaseUrlTest: null,
      duplicateCheckTest: null,
      databaseUrlTest: null
    };
    
    // Test 1: Firebase URL Generation
    console.log('Test 1: Testing Firebase URL generation...');
    const testContent = 'Test content for 360° fixes verification';
    const testFile = new File([testContent], 'test-fixes-360.jpg', {
      type: 'image/jpeg',
      lastModified: Date.now()
    });
    
    const uploadResult = await uploadFileServer(testFile, '360s', 'test-fixes-360.jpg');
    console.log('Upload result:', uploadResult);
    
    const isFirebaseURL = uploadResult.url.includes('firebasestorage.googleapis.com');
    const isMockFirebaseURL = uploadResult.url.includes('mock-token');
    
    results.firebaseUrlTest = {
      success: uploadResult.success,
      isFirebaseURL,
      isMockFirebaseURL,
      urlFormat: uploadResult.url.substring(0, 100) + '...',
      storageType: uploadResult.storage,
      analysis: isFirebaseURL ? 'Firebase URL generated correctly' : 'Using local storage fallback'
    };
    
    // Test 2: Database URL Saving
    console.log('Test 2: Testing database URL saving...');
    await connectDB();
    
    const testData = {
      name: 'test_fixes_360',
      url: uploadResult.url,
      originalFileName: 'test-fixes-360.jpg',
      priority: 999,
      markerList: [],
      cameraPosition: -0.0001,
      _360Rotation: -0.0001
    };
    
    const new360 = new _360Settings(testData);
    const saved360 = await new360.save();
    
    const savedIsFirebaseURL = saved360.url.includes('firebasestorage.googleapis.com');
    const savedIsMockFirebaseURL = saved360.url.includes('mock-token');
    
    results.databaseUrlTest = {
      success: true,
      recordId: saved360._id.toString(),
      savedUrl: saved360.url.substring(0, 100) + '...',
      isFirebaseURL: savedIsFirebaseURL,
      isMockFirebaseURL: savedIsMockFirebaseURL,
      analysis: savedIsFirebaseURL ? 'Firebase URL saved to database correctly' : 'Local URL saved to database'
    };
    
    // Test 3: Duplicate Check API
    console.log('Test 3: Testing duplicate check API...');
    const duplicateResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/360s/check-duplicates`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ filenames: ['test-fixes-360.jpg'] }),
    });
    
    const duplicateResult = await duplicateResponse.json();
    
    results.duplicateCheckTest = {
      success: duplicateResult.success,
      duplicatesFound: duplicateResult.data?.duplicates?.length || 0,
      duplicateData: duplicateResult.data?.duplicates?.[0] || null,
      analysis: duplicateResult.data?.duplicates?.length > 0 ? 
        'Duplicate detection working - confirmation dialog should appear' : 
        'No duplicates found'
    };
    
    // Cleanup test record
    await _360Settings.findByIdAndDelete(saved360._id);
    console.log('Test record cleaned up');
    
    // Overall analysis
    const overallStatus = {
      firebaseUrlFix: results.firebaseUrlTest.isFirebaseURL ? 'WORKING' : 'NEEDS_REAL_FIREBASE',
      confirmationDialogFix: results.duplicateCheckTest.success ? 'WORKING' : 'FAILED',
      databaseSavingFix: results.databaseUrlTest.isFirebaseURL ? 'WORKING' : 'NEEDS_REAL_FIREBASE'
    };
    
    return NextResponse.json({
      success: true,
      message: '360° fixes test completed',
      results,
      overallStatus,
      recommendations: {
        firebaseUrl: results.firebaseUrlTest.isFirebaseURL ? 
          'Firebase URLs are being generated and saved correctly' : 
          'Configure real Firebase project for production use',
        confirmationDialog: results.duplicateCheckTest.success ? 
          'Duplicate detection API working - confirmation dialogs should appear in UI' : 
          'Check duplicate detection API configuration',
        nextSteps: [
          'Test confirmation dialog in admin interface by uploading duplicate filename',
          'Verify Firebase URLs are displayed correctly in 360° viewer',
          'Configure real Firebase project for production deployment'
        ]
      }
    });
  } catch (error) {
    console.error('360° fixes test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack,
      troubleshooting: {
        possibleCauses: [
          'Database connection issue',
          'Firebase configuration problem',
          'API endpoint error',
          'File upload handler error'
        ],
        nextSteps: [
          'Check server logs for detailed errors',
          'Verify MongoDB connection',
          'Test individual components separately',
          'Check environment variables'
        ]
      }
    }, { status: 500 });
  }
}

// GET /api/test-360-fixes - Show test information
export async function GET(request) {
  return NextResponse.json({
    message: 'Use POST method to test 360° fixes',
    endpoint: '/api/test-360-fixes',
    method: 'POST',
    description: 'Tests both Firebase URL saving and confirmation dialog fixes',
    tests: [
      '1. Firebase URL generation and format validation',
      '2. Database URL saving verification',
      '3. Duplicate check API functionality',
      '4. Overall fix status analysis'
    ]
  });
}
