# 360° Viewer Dashboard - Complete Data Flow Architecture

## Overview
The 360ViewerDashboard is a complex system that manages 360° panoramic images with interactive markers. This document traces the complete data flow from initial load to final database submission.

## Component Hierarchy & Data Flow

```
360ViewerDashboard (Root Component)
├── State Management
│   ├── threeSixties[] (all 360° images from API)
│   ├── _360Object{} (current image + markers state)
│   ├── currentImageIndex (selected image index)
│   └── set_360Object() (state updater function)
│
├── Canvas (Three.js Rendering)
│   ├── PanoramicSphere (3D sphere + Leva controls)
│   │   ├── Receives: _360Object, set_360Object
│   │   ├── Updates: cameraPosition, _360Rotation via Leva
│   │   └── Calls: set_360Object() on control changes
│   │
│   └── _360InfoMarkersDashboard (3D marker rendering + Leva position controls)
│       ├── Receives: markerList, set_360Object
│       ├── Updates: marker x,y,z positions via Leva controls
│       └── Calls: set_360Object() on position changes
│
└── MarkerInputPanel (UI Panel)
    ├── Receives: _360Object, _360sList, set_360Object
    └── MarkersInputList (Marker CRUD operations)
        ├── Receives: _360Object, _360sList, set_360Object
        ├── Local State: markerList[], input{}
        ├── Operations: Add, Delete, Update markers
        └── Submits: PATCH /api/360s/{id} → Database
```

## Detailed Data Flow Stages

### 1. Initial Load & State Setup
```javascript
// 360ViewerDashboard.jsx - Lines 18-24
const [threeSixties, setThreeSixties] = useState([]);
const [_360Object, set_360Object] = useState({
  cameraPosition: 0,
  _360Rotation: 0,
  markerList: [],
});
```

**Flow**:
1. Component mounts → `fetchThreeSixties()` called
2. API call: `GET /api/360s?sort=priority&order=asc&limit=50`
3. Response stored in `threeSixties[]` state
4. `currentImageIndex` set to 0 (first image)

### 2. Current Image Selection & _360Object Sync
```javascript
// 360ViewerDashboard.jsx - Lines 43-63
useEffect(() => {
  const new_360ObjectState = {
    _id: currentImage?._id || '',
    cameraPosition: currentImage?.cameraPosition || 0,
    _360Rotation: currentImage?._360Rotation || 0,
    markerList: currentImage?.markerList || [],
  };
  set_360Object(new_360ObjectState);
}, [currentImage, set_360Object]);
```

**Flow**:
1. User selects image → `currentImageIndex` changes
2. `currentImage` derived from `threeSixties[currentImageIndex]`
3. `_360Object` state updated with current image data
4. All child components receive updated `_360Object`

### 3. Real-time Marker Position Updates (Leva Controls)
```javascript
// _360InfoMarkersDashboard.jsx - Lines 98-119
useEffect(() => {
  debounceTimeoutRef.current = setTimeout(() => {
    set_360Object(prev => ({
      ...prev,
      markerList: prev.markerList.map(m =>
        m.name === safeItem.name ? {...m, ...position} : m
      )
    }))
  }, 100); // 100ms throttle
}, [position, safeItem.name, set_360Object])
```

**Flow**:
1. User drags Leva position controls (x, y, z)
2. Position changes trigger throttled `set_360Object()` call
3. `_360Object.markerList` updated with new positions
4. Visual markers move in real-time in 3D space
5. **Note**: Changes are LOCAL only (not saved to database yet)

### 4. Camera & Rotation Updates (Leva Controls)
```javascript
// PanoramicSphereDashbard.jsx - Lines 250-287
onChange: (value) => {
  debounceTimeoutRef.current = setTimeout(() => {
    set_360Object(prev => ({ ...prev, cameraPosition: value }));
  }, 50); // 50ms throttle
}
```

**Flow**:
1. User adjusts camera/rotation Leva controls
2. Throttled updates to `_360Object.cameraPosition` and `_360Object._360Rotation`
3. Visual changes applied immediately
4. **Note**: Changes are LOCAL only (not saved to database yet)

### 5. Marker CRUD Operations (MarkersInputList)
```javascript
// MarkersInputList.jsx - Lines 22-29
const currentMarkerList = useMemo(() => {
  return _360Object?.markerList || []
}, [_360Object?.markerList])

useEffect(() => {
  setMarkerList(currentMarkerList)
}, [currentMarkerList])
```

**Flow**:
1. `MarkersInputList` receives `_360Object` from parent
2. Local `markerList` state synced with `_360Object.markerList`
3. User can:
   - **Add**: New markers via input form → `handleAddList()`
   - **Delete**: Remove markers → `handleDeleteMarker()`
   - **Update**: Modify marker properties → `handleUpdateMarker()`
4. All changes update local `markerList` state
5. **Note**: Changes are LOCAL only until submission

### 6. Final Database Submission
```javascript
// MarkersInputList.jsx - Lines 32-58
const handleSubmit = useCallback(async () => {
  const response = await fetch(`/api/360s/${_360Object?._id}`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ markerList: markerList }),
  })
  
  if (result.success) {
    set_360Object(result.data) // Update parent state with DB response
  }
}, [_360Object, markerList, set_360Object])
```

**Flow**:
1. User clicks "Submit" button
2. PATCH request sent to `/api/360s/{id}` with current `markerList`
3. Database updated with new marker data
4. API returns updated document
5. `set_360Object(result.data)` updates parent state
6. All components re-render with fresh database data

## State Synchronization Points

### Local vs Database State
- **Local State**: `_360Object` in 360ViewerDashboard
- **Component State**: `markerList` in MarkersInputList
- **Database State**: MongoDB document via API

### Sync Mechanisms
1. **Load**: Database → Local State (on image selection)
2. **Real-time**: Leva Controls → Local State (immediate visual feedback)
3. **CRUD**: MarkersInputList → Component State (temporary changes)
4. **Submit**: Component State → Database → Local State (persistence)

## Critical Integration Points

### 1. Prop Passing Chain
```
360ViewerDashboard.set_360Object
  ↓
MarkerInputPanel.set_360Object
  ↓
MarkersInputList.set_360Object
```

### 2. State Update Sources
- **Leva Controls**: Direct `set_360Object()` calls (real-time)
- **Image Selection**: `useEffect` on `currentImage` change
- **Database Response**: `set_360Object(result.data)` after submission

### 3. Rendering Dependencies
- **PanoramicSphere**: Depends on `_360Object.cameraPosition`, `_360Object._360Rotation`
- **_360InfoMarkers**: Depends on `_360Object.markerList`
- **MarkersInputList**: Depends on `_360Object.markerList` for initial sync

## Performance Optimizations

### 1. Throttling/Debouncing
- **Leva Controls**: 50-100ms throttling to prevent excessive updates
- **Marker Updates**: 150ms debouncing in MarkersInputList

### 2. Memoization
- **Props Objects**: `useMemo()` for complex prop objects
- **Component Rendering**: `memo()` for expensive components
- **Derived State**: `useMemo()` for computed values

### 3. State Management
- **Minimal Re-renders**: Careful dependency arrays
- **Batched Updates**: React's automatic batching
- **Selective Updates**: Only update changed markers

## Error Handling & Edge Cases

### 1. Missing Data
- Default empty arrays for `markerList`
- Fallback values for undefined properties
- Safe navigation with optional chaining

### 2. API Failures
- Error states in submission handling
- Console logging for debugging
- Graceful degradation

### 3. State Inconsistencies
- Cleanup timeouts on unmount
- Proper dependency arrays
- State validation before API calls

This architecture ensures smooth real-time interaction while maintaining data integrity through controlled database persistence.
