'use client';

import { useState } from 'react';
import ClientBookingHistory from './ClientBookingHistory';
import ClientPaymentHistory from './ClientPaymentHistory';
import ClientCommunications from './ClientCommunications';
import EditClientModal from './EditClientModal';

export default function ClientProfile({ 
  client, 
  isLoading, 
  onClientUpdate, 
  onClientDelete, 
  onBack 
}) {
  const [activeTab, setActiveTab] = useState('overview');
  const [showEditModal, setShowEditModal] = useState(false);

  if (isLoading || !client) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusBadge = (client) => {
    if (client.isBlocked) {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Blocked</span>;
    }
    if (!client.isActive) {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Inactive</span>;
    }
    return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>;
  };

  const getRoleBadge = (role) => {
    const roleConfig = {
      guest: { bg: 'bg-gray-100', text: 'text-gray-800' },
      user: { bg: 'bg-blue-100', text: 'text-blue-800' },
      manager: { bg: 'bg-purple-100', text: 'text-purple-800' },
      admin: { bg: 'bg-red-100', text: 'text-red-800' },
    };

    const config = roleConfig[role] || roleConfig.user;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </span>
    );
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '👤' },
    { id: 'bookings', name: 'Bookings', icon: '📅', count: client.bookings?.length },
    { id: 'payments', name: 'Payments', icon: '💳', count: client.payments?.length },
    { id: 'communications', name: 'Communications', icon: '💬' },
  ];

  return (
    <div className="space-y-6">
      {/* Client Header */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                <span className="text-xl font-medium text-gray-700">
                  {client.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{client.name}</h1>
                <div className="flex items-center space-x-2 mt-1">
                  {getStatusBadge(client)}
                  {getRoleBadge(client.role)}
                  {client.isGuest && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      Guest Account
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => setShowEditModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Edit Client
              </button>
              <a
                href={`mailto:${client.email}`}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                Send Email
              </a>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="px-6 py-4 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {client.stats?.bookings?.totalBookings || 0}
              </div>
              <div className="text-sm text-gray-500">Total Bookings</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(client.stats?.bookings?.totalSpent || 0)}
              </div>
              <div className="text-sm text-gray-500">Total Spent</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {client.stats?.bookings?.upcomingBookings || 0}
              </div>
              <div className="text-sm text-gray-500">Upcoming</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {client.loginCount || 0}
              </div>
              <div className="text-sm text-gray-500">Login Count</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Contact Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email</label>
                    <div className="text-sm text-gray-900">{client.email}</div>
                  </div>
                  {client.phone && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Phone</label>
                      <div className="text-sm text-gray-900">{client.phone}</div>
                    </div>
                  )}
                  {client.address && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Address</label>
                      <div className="text-sm text-gray-900">
                        {client.address.street && <div>{client.address.street}</div>}
                        <div>
                          {[client.address.city, client.address.state, client.address.postalCode]
                            .filter(Boolean)
                            .join(', ')}
                        </div>
                        {client.address.country && <div>{client.address.country}</div>}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Account Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Member Since</label>
                    <div className="text-sm text-gray-900">{formatDate(client.createdAt)}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Last Login</label>
                    <div className="text-sm text-gray-900">
                      {client.lastLogin ? formatDate(client.lastLogin) : 'Never'}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Provider</label>
                    <div className="text-sm text-gray-900 capitalize">{client.provider || 'Email'}</div>
                  </div>
                  {client.emergencyContact && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Emergency Contact</label>
                      <div className="text-sm text-gray-900">
                        {client.emergencyContact.name} ({client.emergencyContact.relationship})
                        <br />
                        {client.emergencyContact.phone}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'bookings' && (
            <ClientBookingHistory bookings={client.bookings || []} />
          )}

          {activeTab === 'payments' && (
            <ClientPaymentHistory payments={client.payments || []} />
          )}

          {activeTab === 'communications' && (
            <ClientCommunications clientId={client._id} />
          )}
        </div>
      </div>

      {/* Edit Client Modal */}
      {showEditModal && (
        <EditClientModal
          client={client}
          onClientUpdate={onClientUpdate}
          onClose={() => setShowEditModal(false)}
        />
      )}
    </div>
  );
}
