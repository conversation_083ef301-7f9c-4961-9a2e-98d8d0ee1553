import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';
import { Booking } from '@/models/Booking';
import { Payment } from '@/models/Payment';

// GET /api/clients - Get all clients with search and filtering (no authentication required)
export async function GET(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const role = searchParams.get('role');
    const status = searchParams.get('status');
    const bookingStatus = searchParams.get('bookingStatus');
    const paymentStatus = searchParams.get('paymentStatus');
    const location = searchParams.get('location');
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-createdAt';
    const includeStats = searchParams.get('includeStats') === 'true';
    
    // Build query
    const query = {};
    
    // Search by name, email, or phone
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Filter by role
    if (role) {
      query.role = role;
    }
    
    // Filter by account status
    if (status === 'active') {
      query.isActive = true;
      query.isBlocked = false;
    } else if (status === 'inactive') {
      query.isActive = false;
    } else if (status === 'blocked') {
      query.isBlocked = true;
    }
    
    // Filter by location
    if (location) {
      query.$or = [
        { 'address.city': { $regex: location, $options: 'i' } },
        { 'address.state': { $regex: location, $options: 'i' } },
        { 'address.country': { $regex: location, $options: 'i' } },
      ];
    }
    
    // Execute query with pagination
    const skip = (page - 1) * limit;
    let clientsQuery = User.find(query)
      .select('-password')
      .sort(sort)
      .skip(skip)
      .limit(limit);
    
    const clients = await clientsQuery.lean();
    
    // Get total count for pagination
    const total = await User.countDocuments(query);
    
    // Add booking and payment statistics if requested
    if (includeStats) {
      for (let client of clients) {
        // Get booking stats
        const bookingStats = await Booking.aggregate([
          { $match: { customer: client._id } },
          {
            $group: {
              _id: null,
              totalBookings: { $sum: 1 },
              totalSpent: { $sum: '$pricing.totalAmount' },
              lastBooking: { $max: '$createdAt' },
              upcomingBookings: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gte: ['$dates.checkIn', new Date()] },
                        { $in: ['$status', ['pending', 'confirmed']] }
                      ]
                    },
                    1,
                    0
                  ]
                }
              },
            },
          },
        ]);
        
        // Get payment stats
        const paymentStats = await Payment.aggregate([
          { $match: { customer: client._id } },
          {
            $group: {
              _id: '$status',
              count: { $sum: 1 },
              amount: { $sum: '$amount' },
            },
          },
        ]);
        
        client.stats = {
          bookings: bookingStats[0] || {
            totalBookings: 0,
            totalSpent: 0,
            lastBooking: null,
            upcomingBookings: 0,
          },
          payments: paymentStats.reduce((acc, stat) => {
            acc[stat._id] = {
              count: stat.count,
              amount: stat.amount,
            };
            return acc;
          }, {}),
        };
      }
    }
    
    // Filter by booking status if specified
    if (bookingStatus) {
      const bookingFilter = await Booking.find({
        status: bookingStatus,
      }).distinct('customer');
      
      const filteredClients = clients.filter(client =>
        bookingFilter.some(customerId => customerId.toString() === client._id.toString())
      );
      
      return NextResponse.json({
        success: true,
        data: filteredClients,
        pagination: {
          page,
          limit,
          total: filteredClients.length,
          pages: Math.ceil(filteredClients.length / limit),
        },
      });
    }
    
    // Filter by payment status if specified
    if (paymentStatus) {
      const paymentFilter = await Payment.find({
        status: paymentStatus,
      }).distinct('customer');
      
      const filteredClients = clients.filter(client =>
        paymentFilter.some(customerId => customerId.toString() === client._id.toString())
      );
      
      return NextResponse.json({
        success: true,
        data: filteredClients,
        pagination: {
          page,
          limit,
          total: filteredClients.length,
          pages: Math.ceil(filteredClients.length / limit),
        },
      });
    }
    
    return NextResponse.json({
      success: true,
      data: clients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching clients:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch clients',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/clients - Create new client (no authentication required)
export async function POST(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['name', 'email'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `${field} is required`,
          },
          { status: 400 }
        );
      }
    }
    
    // Check if user already exists
    const existingUser = await User.findOne({ email: body.email });
    if (existingUser) {
      return NextResponse.json(
        {
          success: false,
          error: 'Duplicate Error',
          message: 'User with this email already exists',
        },
        { status: 409 }
      );
    }
    
    // Create new client
    const clientData = {
      ...body,
      role: body.role || 'user',
      provider: 'admin_created',
    };
    
    const newClient = new User(clientData);
    await newClient.save();
    
    // Remove password from response
    const clientResponse = newClient.toObject();
    delete clientResponse.password;
    
    return NextResponse.json(
      {
        success: true,
        data: clientResponse,
        message: 'Client created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating client:', error);
    
    if (error.code === 11000) {
      return NextResponse.json(
        {
          success: false,
          error: 'Duplicate Error',
          message: 'Client with this email already exists',
        },
        { status: 409 }
      );
    }
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create client',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/clients - Bulk update clients (no authentication required)
export async function PUT(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    const { clients, action } = body;
    
    if (!Array.isArray(clients)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'clients must be an array',
        },
        { status: 400 }
      );
    }
    
    const results = [];
    
    for (const clientData of clients) {
      try {
        const { _id, ...updateData } = clientData;
        
        // Handle bulk actions
        if (action) {
          switch (action) {
            case 'activate':
              updateData.isActive = true;
              updateData.isBlocked = false;
              break;
            case 'deactivate':
              updateData.isActive = false;
              break;
            case 'block':
              updateData.isBlocked = true;
              break;
            case 'unblock':
              updateData.isBlocked = false;
              break;
          }
        }
        
        const updatedClient = await User.findByIdAndUpdate(
          _id,
          updateData,
          { new: true, runValidators: true }
        ).select('-password');
        
        if (updatedClient) {
          results.push({
            id: _id,
            success: true,
            data: updatedClient,
          });
        } else {
          results.push({
            id: _id,
            success: false,
            error: 'Client not found',
          });
        }
      } catch (error) {
        results.push({
          id: clientData._id,
          success: false,
          error: error.message,
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed',
    });
  } catch (error) {
    console.error('Error bulk updating clients:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update clients',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
