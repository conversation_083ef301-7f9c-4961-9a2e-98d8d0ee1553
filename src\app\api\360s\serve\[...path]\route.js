import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

/**
 * Serve 360° images with proper CORS headers for production
 * This API route handles serving local 360° images when Firebase is not available
 */

// GET /api/360s/serve/[...path] - Serve 360° images with proper headers
export async function GET(request, { params }) {
  try {
    const { path: imagePath } = await params;
    
    if (!imagePath || !Array.isArray(imagePath)) {
      return NextResponse.json(
        { error: 'Invalid image path' },
        { status: 400 }
      );
    }

    // Reconstruct the file path
    const filePath = imagePath.join('/');
    
    // Security: Prevent directory traversal
    if (filePath.includes('..') || filePath.includes('\\')) {
      return NextResponse.json(
        { error: 'Invalid file path' },
        { status: 400 }
      );
    }

    // Try different possible locations for the image
    const possiblePaths = [
      path.join(process.cwd(), 'public', 'uploads', '360s', filePath),
      path.join(process.cwd(), 'public', 'assets', '360s', filePath),
      path.join(process.cwd(), 'public', filePath),
    ];

    let fileBuffer = null;
    let actualPath = null;
    let mimeType = 'image/jpeg'; // Default

    // Try to find the file in possible locations
    for (const possiblePath of possiblePaths) {
      try {
        await fs.access(possiblePath);
        fileBuffer = await fs.readFile(possiblePath);
        actualPath = possiblePath;
        
        // Determine MIME type based on file extension
        const ext = path.extname(possiblePath).toLowerCase();
        switch (ext) {
          case '.png':
            mimeType = 'image/png';
            break;
          case '.jpg':
          case '.jpeg':
            mimeType = 'image/jpeg';
            break;
          case '.webp':
            mimeType = 'image/webp';
            break;
          case '.tiff':
          case '.tif':
            mimeType = 'image/tiff';
            break;
          default:
            mimeType = 'image/jpeg';
        }
        
        break;
      } catch (error) {
        // File not found in this location, try next
        continue;
      }
    }

    if (!fileBuffer) {
      console.error(`360° image not found: ${filePath}`);
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    console.log(`Serving 360° image: ${actualPath}`);

    // Create response with proper headers for 360° images
    const response = new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': mimeType,
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'public, max-age=31536000, immutable', // Cache for 1 year
        'Access-Control-Allow-Origin': '*', // Allow CORS for Three.js
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cross-Origin-Resource-Policy': 'cross-origin',
        'X-Content-Type-Options': 'nosniff',
      },
    });

    return response;

  } catch (error) {
    console.error('Error serving 360° image:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(request) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400', // 24 hours
    },
  });
}

// Handle HEAD requests for image validation
export async function HEAD(request, { params }) {
  try {
    const { path: imagePath } = await params;
    
    if (!imagePath || !Array.isArray(imagePath)) {
      return new NextResponse(null, { status: 400 });
    }

    const filePath = imagePath.join('/');
    
    // Security check
    if (filePath.includes('..') || filePath.includes('\\')) {
      return new NextResponse(null, { status: 400 });
    }

    // Try to find the file
    const possiblePaths = [
      path.join(process.cwd(), 'public', 'uploads', '360s', filePath),
      path.join(process.cwd(), 'public', 'assets', '360s', filePath),
      path.join(process.cwd(), 'public', filePath),
    ];

    for (const possiblePath of possiblePaths) {
      try {
        const stats = await fs.stat(possiblePath);
        
        // Determine MIME type
        const ext = path.extname(possiblePath).toLowerCase();
        let mimeType = 'image/jpeg';
        switch (ext) {
          case '.png': mimeType = 'image/png'; break;
          case '.jpg':
          case '.jpeg': mimeType = 'image/jpeg'; break;
          case '.webp': mimeType = 'image/webp'; break;
          case '.tiff':
          case '.tif': mimeType = 'image/tiff'; break;
        }

        return new NextResponse(null, {
          status: 200,
          headers: {
            'Content-Type': mimeType,
            'Content-Length': stats.size.toString(),
            'Cache-Control': 'public, max-age=31536000, immutable',
            'Access-Control-Allow-Origin': '*',
            'Last-Modified': stats.mtime.toUTCString(),
          },
        });
      } catch (error) {
        continue;
      }
    }

    return new NextResponse(null, { status: 404 });

  } catch (error) {
    console.error('Error in HEAD request for 360° image:', error);
    return new NextResponse(null, { status: 500 });
  }
}
