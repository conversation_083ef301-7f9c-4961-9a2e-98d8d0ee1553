import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Blog } from '@/models/InfoMarker';

// GET /api/info-markers/[id] - Get single info marker (no authentication required)
export async function GET(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    
    const item = await Blog.findById(id);
    
    if (!item) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Info marker not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: item,
    });
  } catch (error) {
    console.error('Error fetching info marker:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch info marker',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/info-markers/[id] - Update info marker (no authentication required)
export async function PUT(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    
    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;
    
    const updatedInfoMarker = await Blog.findByIdAndUpdate(
      id,
      body,
      { 
        new: true, 
        runValidators: true 
      }
    );
    
    if (!updatedInfoMarker) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Info marker not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedInfoMarker,
      message: 'Info marker updated successfully',
    });
  } catch (error) {
    console.error('Error updating info marker:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update info marker',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PATCH /api/info-markers/[id] - Partial update info marker with operations (no authentication required)
export async function PATCH(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();

    const infoMarker = await Blog.findById(id);

    if (!infoMarker) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Info marker not found',
        },
        { status: 404 }
      );
    }

    // Handle specific operations
    if (body.operation) {
      switch (body.operation) {
        case 'addSecondaryEntry':
          if (body.secondaryEntry) {
            // Validate secondary entry structure
            const { image, title, body: entryBody, body2 } = body.secondaryEntry;
            if (!image || !title || !entryBody || !body2) {
              return NextResponse.json(
                {
                  success: false,
                  error: 'Validation Error',
                  message: 'Secondary entry must have image, title, body, and body2 fields',
                },
                { status: 400 }
              );
            }

            infoMarker.secondaryEntries.push({
              image,
              title,
              body: entryBody,
              body2
            });
          }
          break;

        case 'updateSecondaryEntry':
          if (body.entryIndex !== undefined && body.secondaryEntry) {
            if (body.entryIndex >= 0 && body.entryIndex < infoMarker.secondaryEntries.length) {
              Object.assign(infoMarker.secondaryEntries[body.entryIndex], body.secondaryEntry);
            }
          }
          break;

        case 'removeSecondaryEntry':
          if (body.entryIndex !== undefined) {
            if (body.entryIndex >= 0 && body.entryIndex < infoMarker.secondaryEntries.length) {
              infoMarker.secondaryEntries.splice(body.entryIndex, 1);
            }
          }
          break;

        default:
          return NextResponse.json(
            {
              success: false,
              error: 'Invalid Operation',
              message: 'Unsupported operation',
            },
            { status: 400 }
          );
      }
    } else {
      // Regular partial update
      Object.assign(infoMarker, body);
    }

    await infoMarker.save();

    return NextResponse.json({
      success: true,
      data: infoMarker,
      message: 'Info marker updated successfully',
    });
  } catch (error) {
    console.error('Error updating info marker:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update info marker',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// DELETE /api/info-markers/[id] - Delete info marker (no authentication required)
export async function DELETE(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;

    const deletedInfoMarker = await Blog.findByIdAndDelete(id);

    if (!deletedInfoMarker) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Info marker not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: deletedInfoMarker,
      message: 'Info marker deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting info marker:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete info marker',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
