'use client';

import React, { useState, useCallback } from 'react';
import PagesForm from './PagesForm';
import PagesList from './PagesList';

const PagesManagement = React.memo(() => {
  const [currentView, setCurrentView] = useState('list'); // 'list' | 'create' | 'edit'
  const [selectedPage, setSelectedPage] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [notification, setNotification] = useState(null);

  // Show notification helper
  const showNotification = useCallback((message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  }, []);

  // Handle create new page
  const handleCreate = useCallback(() => {
    setSelectedPage(null);
    setCurrentView('create');
  }, []);

  // Handle edit page
  const handleEdit = useCallback((page) => {
    setSelectedPage(page);
    setCurrentView('edit');
  }, []);

  // Handle save page (create or update)
  const handleSave = useCallback(async (formData) => {
    setIsLoading(true);
    try {
      const url = selectedPage 
        ? `/api/pages/${selectedPage._id}`
        : '/api/pages';
      
      const method = selectedPage ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        showNotification(
          selectedPage 
            ? 'Page updated successfully' 
            : 'Page created successfully'
        );
        setCurrentView('list');
        setRefreshTrigger(prev => prev + 1);
      } else {
        throw new Error(data.message || 'Failed to save page');
      }
    } catch (error) {
      console.error('Save error:', error);
      showNotification(error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [selectedPage, showNotification]);

  // Handle delete page
  const handleDelete = useCallback(async (pageId) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/pages/${pageId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        showNotification('Page deleted successfully');
        setRefreshTrigger(prev => prev + 1);
      } else {
        throw new Error(data.message || 'Failed to delete page');
      }
    } catch (error) {
      console.error('Delete error:', error);
      showNotification(error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showNotification]);

  // Handle bulk delete
  const handleBulkDelete = useCallback(async (pageIds) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/pages?ids=${pageIds.join(',')}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        showNotification(`Deleted ${data.deletedCount} pages successfully`);
        setRefreshTrigger(prev => prev + 1);
      } else {
        throw new Error(data.message || 'Failed to delete pages');
      }
    } catch (error) {
      console.error('Bulk delete error:', error);
      showNotification(error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [showNotification]);

  // Handle cancel form
  const handleCancel = useCallback(() => {
    setSelectedPage(null);
    setCurrentView('list');
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="px-4 py-6 sm:px-0">
          <div className="border-b border-gray-200 pb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-trasandina-black leading-tight text-gray-900 uppercase tracking-wide">
                  Pages Management
                </h1>
                <p className="mt-3 text-lg font-trasandina-light text-gray-600 leading-relaxed">
                  Manage content for the four main navbar sections: The Island, Experiences, Testimonials, and Location & Contacts.
                </p>
              </div>
              
              {currentView === 'list' && (
                <button
                  onClick={handleCreate}
                  className="px-6 py-3 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Create New Page
                </button>
              )}
            </div>
          </div>

          {/* View Navigation */}
          <div className="mt-6 flex items-center space-x-4">
            <nav className="flex space-x-8">
              <button
                onClick={() => setCurrentView('list')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  currentView === 'list'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Pages List
              </button>
              {(currentView === 'create' || currentView === 'edit') && (
                <button
                  onClick={() => {}} // This is just for display, actual navigation handled by form
                  className="py-2 px-1 border-b-2 border-blue-500 text-blue-600 font-medium text-sm"
                >
                  {currentView === 'create' ? 'Create Page' : 'Edit Page'}
                </button>
              )}
            </nav>
          </div>
        </div>

        {/* Notification */}
        {notification && (
          <div className={`mb-6 p-4 rounded-md ${
            notification.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-700'
              : 'bg-red-50 border border-red-200 text-red-700'
          }`}>
            <div className="flex items-center justify-between">
              <span>{notification.message}</span>
              <button
                onClick={() => setNotification(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {/* Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-700">Processing...</span>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="px-4 sm:px-0">
          {currentView === 'list' && (
            <PagesList
              onEdit={handleEdit}
              onDelete={handleDelete}
              onBulkDelete={handleBulkDelete}
              refreshTrigger={refreshTrigger}
            />
          )}

          {(currentView === 'create' || currentView === 'edit') && (
            <PagesForm
              page={selectedPage}
              onSave={handleSave}
              onCancel={handleCancel}
              isLoading={isLoading}
            />
          )}
        </div>

        {/* Footer Info */}
        <div className="mt-8 px-4 sm:px-0">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">
              Pages Management Information
            </h3>
            <div className="text-sm text-blue-700 space-y-1">
              <p>• <strong>The Island & Experiences:</strong> Support main content plus additional secondary entries</p>
              <p>• <strong>Testimonials:</strong> Manage customer testimonials with unique names</p>
              <p>• <strong>Location & Contacts:</strong> Single page with contact information and external URL</p>
              <p>• All images are stored in Firebase Storage under the 'elephantisland/pages/' folder</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

PagesManagement.displayName = 'PagesManagement';

export default PagesManagement;
