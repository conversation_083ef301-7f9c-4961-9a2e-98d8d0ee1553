import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
import { User } from '@/models/User';
import { 
  sendBookingConfirmationEmail, 
  sendPaymentReminderEmail, 
  sendBookingCancellationEmail 
} from '@/lib/email-service';

/**
 * Schedule booking confirmation email (sent immediately)
 */
export async function scheduleBookingConfirmationEmail(bookingId) {
  try {
    await connectDB();
    
    const booking = await Booking.findById(bookingId)
      .populate('customer')
      .populate('package');
    
    if (!booking) {
      throw new Error('Booking not found');
    }
    
    const result = await sendBookingConfirmationEmail(booking, booking.customer);
    
    // Log the email in booking communications
    if (result.success) {
      await booking.addCommunication({
        type: 'email',
        direction: 'outbound',
        subject: `Booking Confirmation - ${booking.bookingNumber}`,
        content: 'Booking confirmation email sent successfully',
        timestamp: new Date(),
      });
    }
    
    return result;
  } catch (error) {
    console.error('Error scheduling booking confirmation email:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Schedule payment reminder email (4 hours after booking creation)
 */
export async function schedulePaymentReminderEmail(bookingId, delayHours = 4) {
  try {
    await connectDB();
    
    const booking = await Booking.findById(bookingId)
      .populate('customer')
      .populate('package');
    
    if (!booking) {
      throw new Error('Booking not found');
    }
    
    // Check if payment is already completed
    if (['paid', 'completed'].includes(booking.payment.status)) {
      console.log(`Payment already completed for booking ${booking.bookingNumber}, skipping reminder`);
      return { success: true, skipped: true, reason: 'Payment already completed' };
    }
    
    // Check if booking is still pending
    if (!['pending'].includes(booking.status)) {
      console.log(`Booking ${booking.bookingNumber} is no longer pending, skipping reminder`);
      return { success: true, skipped: true, reason: 'Booking no longer pending' };
    }
    
    const result = await sendPaymentReminderEmail(booking, booking.customer);
    
    // Log the email in booking communications
    if (result.success) {
      await booking.addCommunication({
        type: 'email',
        direction: 'outbound',
        subject: `Payment Reminder - ${booking.bookingNumber}`,
        content: 'Payment reminder email sent',
        timestamp: new Date(),
      });
    }
    
    return result;
  } catch (error) {
    console.error('Error scheduling payment reminder email:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Cancel booking and send cancellation email (48 hours after booking creation)
 */
export async function cancelUnpaidBooking(bookingId) {
  try {
    await connectDB();
    
    const booking = await Booking.findById(bookingId)
      .populate('customer')
      .populate('package');
    
    if (!booking) {
      throw new Error('Booking not found');
    }
    
    // Check if payment is already completed
    if (['paid', 'completed'].includes(booking.payment.status)) {
      console.log(`Payment already completed for booking ${booking.bookingNumber}, skipping cancellation`);
      return { success: true, skipped: true, reason: 'Payment already completed' };
    }
    
    // Check if booking is already cancelled
    if (booking.status === 'cancelled') {
      console.log(`Booking ${booking.bookingNumber} already cancelled`);
      return { success: true, skipped: true, reason: 'Already cancelled' };
    }
    
    // Cancel the booking
    booking.status = 'cancelled';
    booking.cancellation = {
      cancelledAt: new Date(),
      reason: 'Payment not completed within 48 hours',
      refundAmount: 0,
      refundStatus: 'none',
    };
    
    await booking.save();
    
    // Send cancellation email
    const emailResult = await sendBookingCancellationEmail(
      booking, 
      booking.customer, 
      'Payment not completed within 48 hours'
    );
    
    // Log the cancellation and email
    await booking.addCommunication({
      type: 'email',
      direction: 'outbound',
      subject: `Booking Cancelled - ${booking.bookingNumber}`,
      content: 'Booking cancelled due to unpaid status. Cancellation email sent.',
      timestamp: new Date(),
    });
    
    console.log(`Booking ${booking.bookingNumber} cancelled due to unpaid status`);
    
    return { 
      success: true, 
      cancelled: true, 
      emailSent: emailResult.success,
      bookingNumber: booking.bookingNumber 
    };
  } catch (error) {
    console.error('Error cancelling unpaid booking:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Process all pending email notifications
 * This function should be called by a cron job or scheduled task
 */
export async function processEmailNotifications() {
  try {
    await connectDB();
    
    const now = new Date();
    const fourHoursAgo = new Date(now.getTime() - 4 * 60 * 60 * 1000);
    const fortyEightHoursAgo = new Date(now.getTime() - 48 * 60 * 60 * 1000);
    
    console.log('Processing email notifications...');
    
    // Find bookings that need payment reminders (4 hours old, payment pending)
    const bookingsNeedingReminders = await Booking.find({
      status: 'pending',
      'payment.status': { $in: ['pending', 'failed'] },
      createdAt: { $lte: fourHoursAgo },
      // Check if reminder hasn't been sent yet
      'communications.subject': { $not: /Payment Reminder/ },
    }).populate('customer').populate('package');
    
    console.log(`Found ${bookingsNeedingReminders.length} bookings needing payment reminders`);
    
    // Send payment reminders
    for (const booking of bookingsNeedingReminders) {
      try {
        await schedulePaymentReminderEmail(booking._id, 0); // No delay, send immediately
        console.log(`Payment reminder sent for booking ${booking.bookingNumber}`);
      } catch (error) {
        console.error(`Failed to send payment reminder for booking ${booking.bookingNumber}:`, error);
      }
    }
    
    // Find bookings that need to be cancelled (48 hours old, payment still pending)
    const bookingsToCancel = await Booking.find({
      status: 'pending',
      'payment.status': { $in: ['pending', 'failed'] },
      createdAt: { $lte: fortyEightHoursAgo },
    }).populate('customer').populate('package');
    
    console.log(`Found ${bookingsToCancel.length} bookings to cancel`);
    
    // Cancel unpaid bookings
    for (const booking of bookingsToCancel) {
      try {
        await cancelUnpaidBooking(booking._id);
        console.log(`Booking ${booking.bookingNumber} cancelled due to unpaid status`);
      } catch (error) {
        console.error(`Failed to cancel booking ${booking.bookingNumber}:`, error);
      }
    }
    
    return {
      success: true,
      remindersSent: bookingsNeedingReminders.length,
      bookingsCancelled: bookingsToCancel.length,
      processedAt: now,
    };
  } catch (error) {
    console.error('Error processing email notifications:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Get email notification status for a booking
 */
export async function getEmailNotificationStatus(bookingId) {
  try {
    await connectDB();
    
    const booking = await Booking.findById(bookingId);
    if (!booking) {
      throw new Error('Booking not found');
    }
    
    const now = new Date();
    const bookingAge = now.getTime() - new Date(booking.createdAt).getTime();
    const hoursOld = bookingAge / (1000 * 60 * 60);
    
    // Check communication history
    const confirmationSent = booking.communications.some(comm => 
      comm.subject && comm.subject.includes('Booking Confirmation')
    );
    
    const reminderSent = booking.communications.some(comm => 
      comm.subject && comm.subject.includes('Payment Reminder')
    );
    
    const cancellationSent = booking.communications.some(comm => 
      comm.subject && comm.subject.includes('Booking Cancelled')
    );
    
    return {
      bookingNumber: booking.bookingNumber,
      status: booking.status,
      paymentStatus: booking.payment.status,
      hoursOld: Math.round(hoursOld * 10) / 10,
      emailStatus: {
        confirmationSent,
        reminderSent,
        cancellationSent,
        reminderDue: hoursOld >= 4 && !reminderSent && booking.status === 'pending',
        cancellationDue: hoursOld >= 48 && booking.status === 'pending',
      },
    };
  } catch (error) {
    console.error('Error getting email notification status:', error);
    return { success: false, error: error.message };
  }
}
