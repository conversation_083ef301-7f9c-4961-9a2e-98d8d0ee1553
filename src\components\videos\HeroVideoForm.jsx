'use client';

import { useState, useEffect } from 'react';
import { MdSave, MdCancel, MdCloudUpload, MdStar, MdStarBorder } from 'react-icons/md';

export default function HeroVideoForm({ 
  heroVideo = null, 
  onSave, 
  onCancel, 
  isLoading = false 
}) {
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    isActive: false,
  });
  const [errors, setErrors] = useState({});
  const [videoFile, setVideoFile] = useState(null);
  const [videoPreview, setVideoPreview] = useState('');
  const [uploading, setUploading] = useState(false);

  // Initialize form data when heroVideo prop changes
  useEffect(() => {
    if (heroVideo) {
      setFormData({
        name: heroVideo.name || '',
        url: heroVideo.url || '',
        isActive: heroVideo.isActive || false,
      });
      setVideoPreview(heroVideo.url || '');
    } else {
      setFormData({
        name: '',
        url: '',
        isActive: false,
      });
      setVideoPreview('');
    }
  }, [heroVideo]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleVideoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setVideoFile(file);
      
      // Create preview URL
      const videoUrl = URL.createObjectURL(file);
      setVideoPreview(videoUrl);
    }
  };

  const uploadVideo = async () => {
    if (!videoFile) return null;
    
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('files', videoFile);
      
      const response = await fetch('/api/upload/hero-videos', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      
      if (result.success && result.data.length > 0) {
        return result.data[0].url;
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Video upload error:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.url && !videoFile) {
      newErrors.url = 'Video is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      let videoUrl = formData.url;
      
      // Upload new video if selected
      if (videoFile) {
        videoUrl = await uploadVideo();
      }
      
      const submitData = {
        ...formData,
        url: videoUrl,
      };
      
      await onSave(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: 'Failed to save hero video. Please try again.' });
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        {heroVideo ? 'Edit Hero Video' : 'Create New Hero Video'}
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter hero video name"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Active Status */}
        <div>
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              name="isActive"
              checked={formData.isActive}
              onChange={handleInputChange}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm font-medium text-gray-700 flex items-center">
              {formData.isActive ? (
                <MdStar className="text-yellow-500 mr-1" />
              ) : (
                <MdStarBorder className="text-gray-400 mr-1" />
              )}
              Set as Active Hero Video
            </span>
          </label>
          <p className="mt-1 text-sm text-gray-500">
            Only one hero video can be active at a time. Setting this as active will deactivate all other hero videos.
          </p>
        </div>

        {/* Video Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Video *
          </label>
          
          {/* Video Preview */}
          {videoPreview && (
            <div className="mb-4">
              <video
                src={videoPreview}
                controls
                className="w-full max-w-md h-48 object-cover rounded-md border border-gray-300"
              >
                Your browser does not support the video tag.
              </video>
            </div>
          )}
          
          {/* File Input */}
          <div className="flex items-center space-x-4">
            <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
              <MdCloudUpload className="mr-2" />
              Choose Hero Video
              <input
                type="file"
                accept="video/*"
                onChange={handleVideoChange}
                className="hidden"
              />
            </label>
            
            {videoFile && (
              <span className="text-sm text-gray-600">
                {videoFile.name}
              </span>
            )}
          </div>
          
          {errors.url && (
            <p className="mt-1 text-sm text-red-600">{errors.url}</p>
          )}
          
          <p className="mt-1 text-sm text-gray-500">
            Recommended: High-quality video suitable for hero section. Supported formats: MP4, WebM. Maximum size: 200MB.
          </p>
        </div>

        {/* URL Field (Alternative) */}
        <div>
          <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-2">
            Video URL (Alternative)
          </label>
          <input
            type="url"
            id="url"
            name="url"
            value={formData.url}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="https://example.com/hero-video.mp4"
          />
          <p className="mt-1 text-sm text-gray-500">
            You can either upload a video file or provide a direct URL to the video.
          </p>
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-600 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 flex items-center"
          >
            <MdCancel className="mr-2" />
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isLoading || uploading}
            className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center ${
              isLoading || uploading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <MdSave className="mr-2" />
            {isLoading || uploading ? 'Saving...' : 'Save Hero Video'}
          </button>
        </div>
      </form>
    </div>
  );
}
