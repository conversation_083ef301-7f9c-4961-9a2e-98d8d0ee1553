'use client'

import { useRef, useEffect } from 'react'
import { extend, useThree, useFrame } from '@react-three/fiber'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'

// Extend the OrbitControls to be available in JSX
extend({ OrbitControls })

/**
 * OptimizedOrbitControls - Custom OrbitControls with performance optimizations
 * Fixes passive event listener warnings and improves performance
 */
export default function OptimizedOrbitControls({
  makeDefault = false,
  camera,
  domElement,
  enablePan = false,
  enableZoom = false,
  enableRotate = true,
  enableDamping = true,
  dampingFactor = 0.05,
  autoRotate = false,
  autoRotateSpeed = 2.0,
  target = [0, 0, 0],
  minDistance = 0,
  maxDistance = Infinity,
  minPolarAngle = 0,
  maxPolarAngle = Math.PI,
  minAzimuthAngle = -Infinity,
  maxAzimuthAngle = Infinity,
  ...props
}) {
  const { camera: defaultCamera, gl, invalidate, events, set, get, size } = useThree()
  const explCamera = camera || defaultCamera
  const explDomElement = domElement || events.connected || gl.domElement
  const controls = useRef()

  useFrame((state, delta) => {
    if (controls.current?.enabled) {
      controls.current.update()
    }
  })

  useEffect(() => {
    if (controls.current) {
      const callback = () => invalidate()
      controls.current.addEventListener('change', callback)
      
      if (makeDefault) {
        const old = get().controls
        set({ controls: controls.current })
        return () => set({ controls: old })
      }
      
      return () => controls.current?.removeEventListener('change', callback)
    }
  }, [makeDefault, invalidate])

  useEffect(() => {
    if (!controls.current || !explDomElement) return

    // Custom event listener setup with passive option
    const handleWheel = (event) => {
      if (!enableZoom) {
        event.preventDefault()
        return
      }
      // Handle zoom logic here if needed
    }

    const handlePointerDown = (event) => {
      if (!enableRotate && !enablePan) {
        event.preventDefault()
        return
      }
      // Handle pointer down logic
    }

    const handlePointerMove = (event) => {
      if (!enableRotate && !enablePan) {
        return
      }
      // Handle pointer move logic
    }

    const handlePointerUp = (event) => {
      // Handle pointer up logic
    }

    // Add passive event listeners where appropriate
    explDomElement.addEventListener('wheel', handleWheel, { passive: !enableZoom })
    explDomElement.addEventListener('pointerdown', handlePointerDown, { passive: false })
    explDomElement.addEventListener('pointermove', handlePointerMove, { passive: true })
    explDomElement.addEventListener('pointerup', handlePointerUp, { passive: true })

    return () => {
      explDomElement.removeEventListener('wheel', handleWheel)
      explDomElement.removeEventListener('pointerdown', handlePointerDown)
      explDomElement.removeEventListener('pointermove', handlePointerMove)
      explDomElement.removeEventListener('pointerup', handlePointerUp)
    }
  }, [explDomElement, enableZoom, enableRotate, enablePan])

  return (
    <orbitControls
      ref={controls}
      args={[explCamera, explDomElement]}
      enablePan={enablePan}
      enableZoom={enableZoom}
      enableRotate={enableRotate}
      enableDamping={enableDamping}
      dampingFactor={dampingFactor}
      autoRotate={autoRotate}
      autoRotateSpeed={autoRotateSpeed}
      target={target}
      minDistance={minDistance}
      maxDistance={maxDistance}
      minPolarAngle={minPolarAngle}
      maxPolarAngle={maxPolarAngle}
      minAzimuthAngle={minAzimuthAngle}
      maxAzimuthAngle={maxAzimuthAngle}
      {...props}
    />
  )
}
