import mongoose from 'mongoose';
const { Schema } = mongoose;

const blogSchema = new Schema({
    image:{type:String,required:true,default:''},
    title:{type:String,required:true,default:''},
    body1:{type:String,required:true,default:''},
    body2:{type:String,required:true,default:''},
    secondaryEntries:{type:Array,default:[]},
},{timestamps:true});

export const Blog = mongoose.models.Blog||mongoose.model('Blog', blogSchema)