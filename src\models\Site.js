import mongoose from 'mongoose';

const SiteSchema = new mongoose.Schema({
  // Site identification
  name: {
    type: String,
    required: true,
    default: 'Elephant Island Lodge',
    trim: true,
  },
  
  // Required menulinks object field
  menulinks: {
    type: Object,
    required: true,
    default: {
      home: [],
      entrance: [],
      firstFloor: [],
      outDoors: [],
      campOutskirts: []
    },
    validate: {
      validator: function(menulinks) {
        // Ensure menulinks is an object and has required properties
        return (
          typeof menulinks === 'object' &&
          menulinks !== null &&
          Array.isArray(menulinks.home) &&
          Array.isArray(menulinks.entrance) &&
          Array.isArray(menulinks.firstFloor) &&
          Array.isArray(menulinks.outDoors) &&
          Array.isArray(menulinks.campOutskirts)
        );
      },
      message: 'menulinks must be an object with home, entrance, firstFloor, outDoors, and campOutskirts arrays'
    }
  },

  // Site configuration
  configuration: {
    siteName: {
      name: {
        type: String,
        default: 'elephantislandbotswana'
      },
      maxim: {
        type: String,
        default: 'experience your world'
      }
    },
    url: {
      type: String,
      default: function() {
        return process.env.NODE_ENV === 'production' 
          ? 'https://victorchelemu.com' 
          : 'https://localhost:3001';
      }
    },
    logo: {
      type: String,
      default: '/assets/elephant_island_logo.png'
    }
  },

  // Landing page settings
  landingPageSettings: {
    videos: [{
      type: String,
      default: '/assets/video/360_Drone_Reverse.mp4'
    }],
    categories: [{
      name: String,
      btnIcons: {
        ov: String,
        off: String
      },
      width: Number,
      height: Number
    }],
    buttons: [{
      name: String,
      btnIcons: {
        ov: String,
        off: String
      },
      width: Number,
      height: Number
    }]
  },

  // Marker settings
  markerSettings: {
    markerTypeIcons: {
      type: Object,
      default: {}
    },
    markerTypes: [{
      type: String
    }],
    locationsList: [{
      type: String
    }],
    markerTargets: [{
      type: String
    }],
    contentTypes: [{
      type: String
    }]
  },

  // Site status
  isActive: {
    type: Boolean,
    default: true
  },

  // Metadata
  version: {
    type: String,
    default: '1.0.0'
  },

  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },

  // Backup of previous settings for rollback
  previousSettings: {
    type: Object,
    default: null
  }

}, {
  timestamps: true,
  // Ensure only one active site configuration
  collection: 'site_settings'
});

// Index for performance
SiteSchema.index({ isActive: 1 });
SiteSchema.index({ name: 1 });

// Pre-save middleware to backup previous settings
SiteSchema.pre('save', async function(next) {
  if (this.isModified() && !this.isNew) {
    // Store previous version for rollback capability
    const previousDoc = await this.constructor.findById(this._id);
    if (previousDoc) {
      this.previousSettings = {
        menulinks: previousDoc.menulinks,
        configuration: previousDoc.configuration,
        modifiedAt: new Date()
      };
    }
  }
  next();
});

// Static method to get or create default site settings
SiteSchema.statics.getOrCreateDefault = async function() {
  let site = await this.findOne({ isActive: true });
  
  if (!site) {
    // Create default site settings based on current settings.jsx
    site = await this.create({
      name: 'Elephant Island Lodge',
      menulinks: {
        home: [],
        entrance: [],
        firstFloor: [],
        outDoors: [],
        campOutskirts: []
      },
      configuration: {
        siteName: {
          name: 'elephantislandbotswana',
          maxim: 'experience your world'
        },
        url: process.env.NODE_ENV === 'production' 
          ? 'https://victorchelemu.com' 
          : 'https://localhost:3001',
        logo: '/assets/elephant_island_logo.png'
      },
      isActive: true
    });
  }
  
  return site;
};

// Instance method to update menu links
SiteSchema.methods.updateMenuLinks = function(newMenuLinks) {
  // Validate the structure
  const requiredKeys = ['home', 'entrance', 'firstFloor', 'outDoors', 'campOutskirts'];
  for (const key of requiredKeys) {
    if (!Array.isArray(newMenuLinks[key])) {
      throw new Error(`menulinks.${key} must be an array`);
    }
  }
  
  this.menulinks = { ...this.menulinks, ...newMenuLinks };
  return this;
};

// Use existing model if it exists, otherwise create a new one
export const Site = mongoose.models.Site || mongoose.model('Site', SiteSchema);
