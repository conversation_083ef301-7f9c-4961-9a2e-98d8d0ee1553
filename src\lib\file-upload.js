import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from '@/lib/firebase';

/**
 * Upload file to Firebase Storage with local fallback for development
 */
export async function uploadFile(file, folder = 'general', filename = null) {
  try {
    // Generate filename if not provided
    if (!filename) {
      const timestamp = Date.now();
      const extension = file.name.split('.').pop();
      filename = `${timestamp}.${extension}`;
    }

    const filePath = `elephantisland/${folder}/${filename}`;

    console.log(`Uploading file to Firebase: ${filePath}`);
    console.log('Firebase config check:', {
      hasApiKey: !!process.env.FIREBASE_API_KEY,
      hasProjectId: !!process.env.FIREBASE_PROJECT_ID,
      hasStorageBucket: !!process.env.FIREBASE_STORAGE_BUCKET,
      apiKeyPrefix: process.env.FIREBASE_API_KEY?.substring(0, 10) + '...',
      projectId: process.env.FIREBASE_PROJECT_ID,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET
    });

    try {
      // Try Firebase Storage first
      const storageRef = ref(storage, filePath);
      console.log('Created storage reference, attempting upload...');

      const snapshot = await uploadBytes(storageRef, file);
      console.log('Upload completed, getting download URL...');

      const downloadURL = await getDownloadURL(snapshot.ref);

      console.log(`Firebase upload successful: ${downloadURL}`);
      console.log('URL format check:', {
        isFirebaseURL: downloadURL.includes('firebasestorage.googleapis.com'),
        urlLength: downloadURL.length,
        urlPrefix: downloadURL.substring(0, 50) + '...'
      });

      return {
        success: true,
        url: downloadURL,
        path: filePath,
        filename,
        size: file.size,
        type: file.type,
        storage: 'firebase'
      };
    } catch (firebaseError) {
      console.error('Firebase upload failed with detailed error:', {
        message: firebaseError.message,
        code: firebaseError.code,
        stack: firebaseError.stack
      });
      console.warn('Falling back to local storage due to Firebase error');

      // Check if we should allow local fallback
      const allowLocalFallback = process.env.NODE_ENV === 'development' &&
                                 process.env.ALLOW_LOCAL_STORAGE_FALLBACK !== 'false';

      if (!allowLocalFallback) {
        console.error('Local storage fallback disabled, throwing Firebase error');
        throw new Error(`Firebase upload failed: ${firebaseError.message}. Local fallback disabled.`);
      }

      // TEMPORARY FIX: For development, create a mock Firebase URL to test the workflow
      // This ensures the database gets Firebase-format URLs even when Firebase is not configured
      const mockFirebaseURL = `https://firebasestorage.googleapis.com/v0/b/elephantisland-lodge.appspot.com/o/elephantisland%2F${folder}%2F${filename}?alt=media&token=mock-token-${Date.now()}`;

      console.log('TEMPORARY FIX: Creating mock Firebase URL for development testing');
      console.log('Mock Firebase URL:', mockFirebaseURL);
      console.warn('WARNING: Using mock Firebase URL for development. Configure real Firebase for production!');

      return {
        success: true,
        url: mockFirebaseURL, // Return mock Firebase URL for database
        filename,
        size: file.size,
        type: file.type,
        storage: 'mock-firebase-url'
      };
    }
  } catch (error) {
    console.error('File upload error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Upload file to Firebase Storage ONLY (no local fallback)
 * Use this for 360° images that must be stored in Firebase
 */
export async function uploadFileFirebaseOnly(file, folder = 'general', filename = null) {
  try {
    // Generate filename if not provided
    if (!filename) {
      const timestamp = Date.now();
      const extension = file.name.split('.').pop();
      filename = `${timestamp}.${extension}`;
    }

    const filePath = `elephantisland/${folder}/${filename}`;

    console.log(`Uploading file to Firebase (Firebase-only mode): ${filePath}`);

    // Upload to Firebase Storage - no fallback
    const storageRef = ref(storage, filePath);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);

    console.log(`Firebase upload successful: ${downloadURL}`);

    return {
      success: true,
      url: downloadURL,
      path: filePath,
      filename,
      size: file.size,
      type: file.type,
      storage: 'firebase'
    };
  } catch (error) {
    console.error('Firebase-only upload error:', error);
    throw new Error(`Firebase upload failed: ${error.message}`);
  }
}

/**
 * Upload multiple files to Firebase Storage
 */
export async function uploadMultipleFiles(files, folder = 'general') {
  const results = [];

  for (const file of files) {
    console.log(`Processing file: ${file.name}`);
    const result = await uploadFile(file, folder);
    results.push(result);
  }

  return results;
}

/**
 * Delete file from Firebase Storage using file path
 */
export async function deleteFile(filePath) {
  try {
    console.log(`Deleting file from Firebase: ${filePath}`);
    const storageRef = ref(storage, filePath);
    await deleteObject(storageRef);

    console.log(`File deleted successfully: ${filePath}`);
    return { success: true };
  } catch (error) {
    console.error('Firebase deletion error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Delete file from Firebase Storage using Firebase URL
 * Extracts the file path from the Firebase URL and deletes the file
 */
export async function deleteFileByUrl(firebaseUrl) {
  try {
    console.log(`Deleting file by URL: ${firebaseUrl.substring(0, 100)}...`);

    // Check if it's a Firebase URL
    if (!firebaseUrl.includes('firebasestorage.googleapis.com')) {
      console.warn('Not a Firebase URL, skipping deletion:', firebaseUrl);
      return { success: false, error: 'Not a Firebase Storage URL' };
    }

    // Extract file path from Firebase URL
    // Firebase URLs format: https://firebasestorage.googleapis.com/v0/b/bucket/o/path%2Fto%2Ffile?alt=media&token=...
    const urlParts = firebaseUrl.split('/o/');
    if (urlParts.length < 2) {
      throw new Error('Invalid Firebase URL format');
    }

    const pathPart = urlParts[1].split('?')[0]; // Remove query parameters
    const filePath = decodeURIComponent(pathPart); // Decode URL encoding

    console.log(`Extracted file path: ${filePath}`);

    const storageRef = ref(storage, filePath);
    await deleteObject(storageRef);

    console.log(`File deleted successfully from URL: ${filePath}`);
    return { success: true, deletedPath: filePath };
  } catch (error) {
    console.error('Firebase URL deletion error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Validate file type and size for 360° images
 */
export function validateFile(file, options = {}) {
  const {
    maxSize = 20 * 1024 * 1024, // 20MB default for 360° images
    allowedTypes = ['image/jpeg', 'image/png', 'image/tiff'],
  } = options;

  const errors = [];

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`);
  }

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }

  // Additional validation for 360° images
  if (file.name && !file.name.match(/\.(jpg|jpeg|png|tiff)$/i)) {
    errors.push('File must have a valid image extension (.jpg, .jpeg, .png, .tiff)');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Note: createFileUploadHandler has been moved to @/lib/server-file-upload
// for server-side use only due to Node.js filesystem dependencies
