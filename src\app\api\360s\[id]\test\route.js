import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';

// Simple test endpoint to check if the route works
export async function PATCH(request, { params }) {
  try {
    console.log('TEST PATCH request received');
    
    const { id } = await params;
    console.log('Test updating ID:', id);
    
    // Just return success without doing anything
    return NextResponse.json({
      success: true,
      message: 'Test endpoint working',
      id: id,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Test endpoint error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Test endpoint failed',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// Simple database test
export async function POST(request, { params }) {
  try {
    console.log('TEST POST request received');
    
    await connectDB();
    console.log('Database connected successfully');
    
    const { id } = await params;
    console.log('Test finding ID:', id);
    
    const item = await _360Settings.findById(id);
    
    return NextResponse.json({
      success: true,
      message: 'Database test successful',
      found: !!item,
      id: id,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Database test failed',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
