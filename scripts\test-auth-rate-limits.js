#!/usr/bin/env node

/**
 * Authentication Rate Limit Testing Script
 * 
 * Tests the authentication system to ensure rate limiting issues are resolved
 */

const https = require('https');

// Configuration
const baseUrl = 'https://localhost:3001';

console.log(`🔐 Testing Authentication Rate Limits on ${baseUrl}`);
console.log('=' .repeat(60));

async function makeRequest(endpoint, method = 'GET', body = null, headers = {}) {
  return new Promise((resolve) => {
    const url = new URL(endpoint, baseUrl);
    
    const options = {
      hostname: url.hostname,
      port: url.port || 3001,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'User-Agent': 'Auth-Rate-Limit-Test/1.0',
        'Content-Type': 'application/json',
        ...headers
      },
      rejectUnauthorized: false // For development HTTPS
    };

    if (body) {
      const bodyString = JSON.stringify(body);
      options.headers['Content-Length'] = Buffer.byteLength(bodyString);
    }

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          endpoint,
          method,
          status: res.statusCode,
          statusText: res.statusMessage,
          headers: res.headers,
          data: data,
          success: res.statusCode >= 200 && res.statusCode < 300,
          rateLimitHeaders: {
            limit: res.headers['x-ratelimit-limit'],
            remaining: res.headers['x-ratelimit-remaining'],
            reset: res.headers['x-ratelimit-reset']
          }
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        endpoint,
        method,
        status: 'ERROR',
        statusText: error.message,
        success: false
      });
    });

    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        endpoint,
        method,
        status: 'TIMEOUT',
        statusText: 'Request timeout',
        success: false
      });
    });

    if (body) {
      req.write(JSON.stringify(body));
    }

    req.end();
  });
}

async function testEssentialAuthEndpoints() {
  console.log('\n🔧 Testing Essential Auth Endpoints (Should NOT be rate limited)...');
  console.log('-'.repeat(50));
  
  const essentialEndpoints = [
    '/api/auth/signin',
    '/api/auth/signout',
    '/api/auth/session',
    '/api/auth/providers',
    '/api/auth/csrf',
    '/api/auth/callback/credentials'
  ];
  
  const results = {};
  
  for (const endpoint of essentialEndpoints) {
    console.log(`Testing ${endpoint}...`);
    const result = await makeRequest(endpoint);
    
    const isRateLimited = result.status === 429;
    const hasUnlimitedRateLimit = result.rateLimitHeaders.limit === 'unlimited';
    
    console.log(`  Status: ${result.status}`);
    console.log(`  Rate Limited: ${isRateLimited ? '❌ YES (PROBLEM!)' : '✅ NO'}`);
    console.log(`  Rate Limit: ${result.rateLimitHeaders.limit || 'Not set'}`);
    console.log(`  Remaining: ${result.rateLimitHeaders.remaining || 'Not set'}`);
    
    results[endpoint] = {
      success: !isRateLimited,
      unlimited: hasUnlimitedRateLimit,
      status: result.status,
      rateLimitHeaders: result.rateLimitHeaders
    };
    
    console.log('');
  }
  
  return results;
}

async function testRapidAuthRequests() {
  console.log('\n⚡ Testing Rapid Authentication Requests...');
  console.log('-'.repeat(50));
  
  const endpoint = '/api/auth/session';
  const requestCount = 10;
  const requests = [];
  
  console.log(`Making ${requestCount} rapid requests to ${endpoint}...`);
  
  // Make multiple rapid requests
  for (let i = 0; i < requestCount; i++) {
    requests.push(makeRequest(endpoint));
  }
  
  const results = await Promise.all(requests);
  
  let successCount = 0;
  let rateLimitedCount = 0;
  
  results.forEach((result, index) => {
    if (result.status === 429) {
      rateLimitedCount++;
      console.log(`  Request ${index + 1}: ❌ Rate Limited (${result.status})`);
    } else {
      successCount++;
      console.log(`  Request ${index + 1}: ✅ Success (${result.status})`);
    }
  });
  
  console.log(`\nResults:`);
  console.log(`  Successful: ${successCount}/${requestCount}`);
  console.log(`  Rate Limited: ${rateLimitedCount}/${requestCount}`);
  console.log(`  Success Rate: ${(successCount / requestCount * 100).toFixed(1)}%`);
  
  return {
    total: requestCount,
    successful: successCount,
    rateLimited: rateLimitedCount,
    successRate: successCount / requestCount
  };
}

async function testSignOutEndpoint() {
  console.log('\n🚪 Testing Sign-out Endpoint Specifically...');
  console.log('-'.repeat(50));
  
  const endpoint = '/api/auth/signout';
  
  // Test multiple sign-out requests
  console.log('Testing multiple sign-out requests...');
  
  for (let i = 1; i <= 5; i++) {
    console.log(`Sign-out attempt ${i}...`);
    const result = await makeRequest(endpoint, 'POST');
    
    console.log(`  Status: ${result.status}`);
    console.log(`  Rate Limited: ${result.status === 429 ? '❌ YES' : '✅ NO'}`);
    console.log(`  Rate Limit Headers: ${JSON.stringify(result.rateLimitHeaders)}`);
    
    if (result.status === 429) {
      console.log(`  ❌ PROBLEM: Sign-out is being rate limited!`);
      return { success: false, rateLimited: true };
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('✅ Sign-out endpoint is working correctly!');
  return { success: true, rateLimited: false };
}

async function testAuthPages() {
  console.log('\n📄 Testing Authentication Pages...');
  console.log('-'.repeat(50));
  
  const pages = [
    '/auth/signin',
    '/auth/signup',
    '/auth/forgot-password'
  ];
  
  const results = {};
  
  for (const page of pages) {
    console.log(`Testing ${page}...`);
    const result = await makeRequest(page);
    
    const isAccessible = result.success;
    const isRateLimited = result.status === 429;
    
    console.log(`  Accessible: ${isAccessible ? '✅ YES' : '❌ NO'}`);
    console.log(`  Rate Limited: ${isRateLimited ? '❌ YES' : '✅ NO'}`);
    console.log(`  Status: ${result.status}`);
    
    results[page] = {
      accessible: isAccessible,
      rateLimited: isRateLimited,
      status: result.status
    };
  }
  
  return results;
}

async function runAllTests() {
  console.log('🚀 Starting Authentication Rate Limit Tests...\n');
  
  const results = {
    essentialEndpoints: await testEssentialAuthEndpoints(),
    rapidRequests: await testRapidAuthRequests(),
    signOutEndpoint: await testSignOutEndpoint(),
    authPages: await testAuthPages()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('=' .repeat(60));
  
  // Essential Endpoints
  console.log('Essential Auth Endpoints:');
  const essentialPassed = Object.values(results.essentialEndpoints).filter(r => r.success).length;
  const essentialTotal = Object.keys(results.essentialEndpoints).length;
  console.log(`  ${essentialPassed}/${essentialTotal} endpoints working correctly`);
  
  // Rapid Requests
  console.log('\nRapid Request Handling:');
  console.log(`  Success Rate: ${(results.rapidRequests.successRate * 100).toFixed(1)}%`);
  console.log(`  Rate Limited: ${results.rapidRequests.rateLimited}/${results.rapidRequests.total}`);
  
  // Sign-out Endpoint
  console.log('\nSign-out Endpoint:');
  console.log(`  Working: ${results.signOutEndpoint.success ? '✅ YES' : '❌ NO'}`);
  console.log(`  Rate Limited: ${results.signOutEndpoint.rateLimited ? '❌ YES' : '✅ NO'}`);
  
  // Auth Pages
  console.log('\nAuthentication Pages:');
  const pagesPassed = Object.values(results.authPages).filter(r => r.accessible && !r.rateLimited).length;
  const pagesTotal = Object.keys(results.authPages).length;
  console.log(`  ${pagesPassed}/${pagesTotal} pages accessible without rate limiting`);
  
  // Overall Assessment
  const allTestsPassed = essentialPassed === essentialTotal &&
                        results.rapidRequests.successRate > 0.8 &&
                        results.signOutEndpoint.success &&
                        !results.signOutEndpoint.rateLimited &&
                        pagesPassed === pagesTotal;
  
  console.log('\n🎯 Overall Assessment:');
  if (allTestsPassed) {
    console.log('✅ Authentication system is working correctly!');
    console.log('✅ Rate limiting issues have been resolved');
    console.log('✅ Sign-out functionality is working properly');
  } else {
    console.log('❌ Authentication system still has issues');
    console.log('❌ Review the failed tests above');
    
    if (results.signOutEndpoint.rateLimited) {
      console.log('❌ CRITICAL: Sign-out is still being rate limited');
    }
  }
  
  console.log('\n💡 Recommendations:');
  if (!allTestsPassed) {
    console.log('- Check middleware configuration for essential auth routes');
    console.log('- Verify sign-out endpoint is excluded from rate limiting');
    console.log('- Test authentication flow manually in browser');
  } else {
    console.log('- Authentication system is ready for production');
    console.log('- Monitor rate limiting in production environment');
    console.log('- Consider implementing user-facing rate limit indicators');
  }
  
  return allTestsPassed;
}

// Handle script execution
if (require.main === module) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { runAllTests, testEssentialAuthEndpoints, testSignOutEndpoint };
