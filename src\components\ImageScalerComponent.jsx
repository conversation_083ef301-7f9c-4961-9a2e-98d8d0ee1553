'use client';
import React, { useRef, useState, useEffect } from 'react';
import { loadUIImageWithCache } from '@/lib/ui-asset-cache';

export default function ImageScalerComponent({
    src,
    alt,
    style,
    onLoad,
    onError,
    ...props
  }) {
    const [isClient, setIsClient] = useState(false);
    const [isLoaded, setIsLoaded] = useState(false);
    const [hasError, setHasError] = useState(false);
    const [sizes, setSizes] = useState({ width: 0, height: 0 });
    const containerRef = useRef(null);
    const imageRef = useRef(null);

    useEffect(() => {
      setIsClient(true)
    },[])

    useEffect(() => {
        if (!isClient || !src) return; // Skip if not client-side yet or no src

        setIsLoaded(false)
        setHasError(false)

        // Use UI asset caching with production URL resolution for better performance
        loadUIImageWithCache(src, {
            id: src.split('/').pop()?.split('.')[0] || 'unknown', // Extract filename as ID
            maxRetries: 2,
            timeout: 5000,
        })
            .then((img) => {
                setIsLoaded(true)
                setSizes({width: img.naturalWidth, height: img.naturalHeight})
                if (onLoad) onLoad()
            })
            .catch((error) => {
                console.warn('ImageScalerComponent - Failed to load image:', src, error)
                setHasError(true)
                setIsLoaded(true) // Still mark as loaded to avoid infinite loading state
                if (onError) onError(error)
            })
    }, [src, isClient, onLoad, onError])

    if(!isClient) {return null}

    // console.log(isClient)
  return (
    <div
      ref={containerRef}
      style={{width:sizes.width,height:sizes.height}}
      className={`flex items-center justify-center ${style} ${isLoaded ? 'opacity-100' : 'opacity-0'} ${hasError ? 'bg-red-100' : ''} transition-[opacity] w-[${sizes.width}px] h-[${sizes.height}px}] duration-300 ease-in-out`}
    >
      <img
        ref={imageRef}
        src={src}
        alt={alt}
        className={`${style ? style : 'object-contain'} w-full h-full`}
        onLoad={onLoad}
        onError={(e) => {
          setHasError(true);
          if (onError) onError(e);
        }}
        {...props}
      />
    </div>
  )
}
