import HeroImageManagement from '@/components/hero-images/HeroImageManagement';
import SettingsManagementDashboard from '@/components/settings/SettingsManagementDashboard';

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

export default async function AdminSettingsPage() {
  // No authentication required - page is accessible to everyone

  return (
    <div className="settings-page h-svh bg-gray-50 overflow-y-auto overflow-hidden">
      <div className="flex flex-col max-w-7xl mx-auto py-6 sm:px-6 lg:px-8 mb-16 gap-5">
        {/* Header */}
        <div className="px-4 py-6 sm:px-0">
          <div className="border-b border-gray-200 pb-4">
            <h1 className="text-3xl font-bold leading-tight text-gray-900">
              Site Settings Management
            </h1>
            <p className="mt-2 text-sm text-gray-600">
              Monitor and manage all payment transactions, refunds, and analytics.
            </p>
          </div>
        </div>

        {/* Payment Management Dashboard */}
        <HeroImageManagement/>
        <SettingsManagementDashboard/>
      </div>
    </div>
  );
}
