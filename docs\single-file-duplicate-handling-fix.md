# Single File Upload Duplicate Handling Fix

## Issue Description
When uploading a single 360° image file that has the same name as an existing file, the system was not checking for duplicates and prompting the user for confirmation. This caused confusion and potential data loss when users expected to be asked whether they wanted to replace an existing file.

## Root Cause
The single file upload mode in `src/components/360s-manager/360Form.jsx` was missing duplicate detection logic. Only the multiple file upload mode had duplicate checking implemented.

## Solution
Added comprehensive duplicate detection and confirmation for single file uploads by:

1. **Added duplicate checking in single upload mode**: Modified `handleSubmit` to check for duplicates before uploading when a new file is selected
2. **Integrated single file duplicate modal**: Added `DuplicateConfirmationModal` for single file duplicate confirmations
3. **Added proper state management**: Extended duplicate check state to handle single file mode
4. **Added required form fields**: Added name and priority fields for single upload mode

## Changes Made

### File: `src/components/360s-manager/360Form.jsx`

#### Import Addition (Line 7):
- Added `DuplicateConfirmationModal` import

#### State Updates (Lines 28-37):
- Extended `duplicateCheck` state with `singleFileMode` and `pendingFile` properties
- Added `showSingleDuplicateModal` state

#### Enhanced handleSubmit Function (Lines 506-567):
- Added duplicate checking for single file uploads
- Integrated with existing duplicate detection API
- Shows single duplicate modal when duplicate is found

#### New Handler Functions (Lines 456-549):
- `handleSingleDuplicateConfirm`: Handles file replacement for single uploads
- `handleSingleDuplicateSkip`: Handles skipping duplicate file
- `handleSingleDuplicateClose`: Handles modal closure

#### UI Enhancements (Lines 668-690):
- Added `DuplicateConfirmationModal` component to JSX
- Properly configured modal props for single file mode

#### Form Fields Addition (Lines 745-792):
- Added Name field (required for single uploads)
- Added Priority field (optional for single uploads)
- Added proper validation and error handling

## Technical Details

### Duplicate Detection Flow:
1. User selects a file in single upload mode
2. System checks if file name already exists using `/api/360s/check-duplicates`
3. If duplicate found, shows `DuplicateConfirmationModal`
4. User can choose to:
   - **Replace**: Uploads new file and updates existing record (preserves markers/camera settings)
   - **Skip**: Cancels the upload operation

### Data Preservation:
When replacing a file, the system:
- Uploads the new image file
- Updates the existing database record with new URL
- Preserves all existing marker data and camera settings
- Maintains the original record ID and metadata

### User Experience:
- Clear duplicate warning with existing file details
- Shows what data will be preserved (markers, camera settings)
- Simple Replace/Skip options
- Loading states during replacement process

## Testing
After the fix:
1. ✅ Single file uploads check for duplicates
2. ✅ Duplicate confirmation modal appears when needed
3. ✅ File replacement works correctly
4. ✅ Data preservation functions properly
5. ✅ Skip option cancels upload safely
6. ✅ Form validation works for name and priority fields

## Impact
- ✅ Prevents accidental file overwrites
- ✅ Provides clear user feedback about duplicates
- ✅ Maintains data integrity during replacements
- ✅ Consistent behavior between single and multiple upload modes
- ✅ Improved user experience with proper form fields

## Files Modified
- `src/components/360s-manager/360Form.jsx`

## Commit Message
Add duplicate detection and confirmation for single file 360° image uploads with proper form fields and data preservation
