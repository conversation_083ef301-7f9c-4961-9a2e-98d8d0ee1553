'use client';

import { useState } from 'react';
import { MdAdd, MdArrowBack } from 'react-icons/md';
import ThreeSixtyForm from './360Form';
import ThreeSixtyList from './360List';
import DeletionConfirmationModal from './DeletionConfirmationModal';

export default function ThreeSixtyManagement() {
  const [currentView, setCurrentView] = useState('list'); // 'list' | 'create' | 'edit'
  const [selectedThreeSixty, setSelectedThreeSixty] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [notification, setNotification] = useState(null);
  const [showDeletionModal, setShowDeletionModal] = useState(false);
  const [imageToDelete, setImageToDelete] = useState(null);

  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleCreate = () => {
    setSelectedThreeSixty(null);
    setCurrentView('create');
  };

  const handleEdit = (threeSixty) => {
    setSelectedThreeSixty(threeSixty);
    setCurrentView('edit');
  };

  const handleSave = async (formData) => {
    setIsLoading(true);
    try {
      const url = selectedThreeSixty 
        ? `/api/360s/${selectedThreeSixty._id}`
        : '/api/360s';
      
      const method = selectedThreeSixty ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        showNotification(
          selectedThreeSixty 
            ? '360° image updated successfully' 
            : '360° image created successfully'
        );
        setCurrentView('list');
        setRefreshTrigger(prev => prev + 1);
      } else {
        throw new Error(data.message || 'Failed to save 360° image');
      }
    } catch (error) {
      console.error('Save error:', error);
      showNotification(error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      // Fetch the 360° image data first
      const response = await fetch(`/api/360s/${id}`);
      const data = await response.json();

      if (data.success) {
        setImageToDelete(data.data);
        setShowDeletionModal(true);
      } else {
        throw new Error(data.message || 'Failed to fetch 360° image data');
      }
    } catch (error) {
      console.error('Error fetching image data:', error);
      showNotification(error.message, 'error');
    }
  };

  const handleDeleteImageOnly = async (imageData) => {
    try {
      const response = await fetch(`/api/360s/${imageData._id}/partial-delete`, {
        method: 'PATCH',
      });

      const data = await response.json();

      if (data.success) {
        showNotification('Image file removed successfully. Markers and settings preserved.');
        setRefreshTrigger(prev => prev + 1);
        setShowDeletionModal(false);
        setImageToDelete(null);

        // Optionally prompt user to upload replacement
        setTimeout(() => {
          if (window.confirm('Would you like to upload a replacement image now?')) {
            handleEdit(data.data);
          }
        }, 1000);
      } else {
        throw new Error(data.message || 'Failed to remove image file');
      }
    } catch (error) {
      console.error('Partial delete error:', error);
      showNotification(error.message, 'error');
    }
  };

  const handleDeleteEverything = async (imageData) => {
    try {
      const response = await fetch(`/api/360s/${imageData._id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        showNotification('360° image and all associated data deleted successfully');
        setRefreshTrigger(prev => prev + 1);
        setShowDeletionModal(false);
        setImageToDelete(null);
      } else {
        throw new Error(data.message || 'Failed to delete 360° image');
      }
    } catch (error) {
      console.error('Complete delete error:', error);
      showNotification(error.message, 'error');
    }
  };

  const handleCloseDeletionModal = () => {
    setShowDeletionModal(false);
    setImageToDelete(null);
  };

  const handleBulkDelete = async (ids) => {
    try {
      const response = await fetch('/api/360s', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      });

      const data = await response.json();

      if (data.success) {
        showNotification(`${data.data.deletedCount} 360° images deleted successfully`);
        setRefreshTrigger(prev => prev + 1);
      } else {
        throw new Error(data.message || 'Failed to delete 360° images');
      }
    } catch (error) {
      console.error('Bulk delete error:', error);
      showNotification(error.message, 'error');
    }
  };

  const handleCancel = () => {
    setCurrentView('list');
    setSelectedThreeSixty(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {currentView !== 'list' && (
            <button
              onClick={handleCancel}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-md"
            >
              <MdArrowBack className="w-5 h-5" />
            </button>
          )}
          <h1 className="text-2xl font-bold text-gray-900">
            {currentView === 'list' && '360° Images Management'}
            {currentView === 'create' && 'Create New 360° Image'}
            {currentView === 'edit' && 'Edit 360° Image'}
          </h1>
        </div>

        {currentView === 'list' && (
          <button
            onClick={handleCreate}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
          >
            <MdAdd className="mr-2" />
            Add 360° Image
          </button>
        )}
      </div>

      {/* Notification */}
      {notification && (
        <div className={`p-4 rounded-md ${
          notification.type === 'success' 
            ? 'bg-green-50 border border-green-200 text-green-700'
            : 'bg-red-50 border border-red-200 text-red-700'
        }`}>
          {notification.message}
        </div>
      )}

      {/* Content */}
      {currentView === 'list' && (
        <ThreeSixtyList
          onEdit={handleEdit}
          onDelete={handleDelete}
          onBulkDelete={handleBulkDelete}
          refreshTrigger={refreshTrigger}
        />
      )}

      {(currentView === 'create' || currentView === 'edit') && (
        <ThreeSixtyForm
          threeSixty={selectedThreeSixty}
          onSave={handleSave}
          onCancel={handleCancel}
          isLoading={isLoading}
        />
      )}

      {/* Deletion Confirmation Modal */}
      <DeletionConfirmationModal
        isOpen={showDeletionModal}
        onClose={handleCloseDeletionModal}
        imageData={imageToDelete}
        onDeleteImageOnly={handleDeleteImageOnly}
        onDeleteEverything={handleDeleteEverything}
      />
    </div>
  );
}
