'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';

const PagesList = React.memo(({ onEdit, onDelete, onBulkDelete, refreshTrigger }) => {
  const [pages, setPages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [sectionFilter, setSectionFilter] = useState('');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({});
  const [selectedPages, setSelectedPages] = useState(new Set());

  // Fetch pages from API
  const fetchPages = useCallback(async () => {
    setIsLoading(true);
    setError('');

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        search: searchTerm,
        section: sectionFilter,
        sortBy,
        sortOrder
      });

      const response = await fetch(`/api/pages?${params}`);
      const data = await response.json();

      if (data.success) {
        setPages(data.data || []);
        setPagination(data.pagination || {});
      } else {
        throw new Error(data.message || 'Failed to fetch pages');
      }
    } catch (error) {
      console.error('Error fetching pages:', error);
      setError(error.message);
      setPages([]);
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, searchTerm, sectionFilter, sortBy, sortOrder]);

  // Effect to fetch pages
  useEffect(() => {
    fetchPages();
  }, [fetchPages, refreshTrigger]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, sectionFilter, sortBy, sortOrder]);

  // Handle search
  const handleSearch = useCallback((value) => {
    setSearchTerm(value);
    setSelectedPages(new Set());
  }, []);

  // Handle section filter
  const handleSectionFilter = useCallback((value) => {
    setSectionFilter(value);
    setSelectedPages(new Set());
  }, []);

  // Handle sorting
  const handleSort = useCallback((field) => {
    if (sortBy === field) {
      setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
    setSelectedPages(new Set());
  }, [sortBy]);

  // Handle page selection
  const handlePageSelect = useCallback((pageId, isSelected) => {
    setSelectedPages(prev => {
      const newSet = new Set(prev);
      if (isSelected) {
        newSet.add(pageId);
      } else {
        newSet.delete(pageId);
      }
      return newSet;
    });
  }, []);

  // Handle select all
  const handleSelectAll = useCallback((isSelected) => {
    if (isSelected) {
      setSelectedPages(new Set(pages.map(page => page._id)));
    } else {
      setSelectedPages(new Set());
    }
  }, [pages]);

  // Handle bulk delete
  const handleBulkDelete = useCallback(async () => {
    if (selectedPages.size === 0) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedPages.size} selected page(s)? This action cannot be undone.`
    );

    if (confirmed) {
      try {
        await onBulkDelete(Array.from(selectedPages));
        setSelectedPages(new Set());
      } catch (error) {
        console.error('Bulk delete error:', error);
      }
    }
  }, [selectedPages, onBulkDelete]);

  // Handle individual delete
  const handleDelete = useCallback(async (page) => {
    const confirmed = window.confirm(
      `Are you sure you want to delete the "${page.section}" page? This action cannot be undone.`
    );

    if (confirmed) {
      try {
        await onDelete(page._id);
        setSelectedPages(prev => {
          const newSet = new Set(prev);
          newSet.delete(page._id);
          return newSet;
        });
      } catch (error) {
        console.error('Delete error:', error);
      }
    }
  }, [onDelete]);

  // Memoized section options
  const sectionOptions = useMemo(() => [
    { value: '', label: 'All Sections' },
    { value: 'the island', label: 'The Island' },
    { value: 'experiences', label: 'Experiences' },
    { value: 'testimonials', label: 'Testimonials' },
    { value: 'location & contacts', label: 'Location & Contacts' }
  ], []);

  // Format section name for display
  const formatSectionName = useCallback((section) => {
    return section.split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }, []);

  // Format date for display
  const formatDate = useCallback((dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <h2 className="text-xl font-trasandina-black text-gray-900 uppercase tracking-wide">
            Pages List
          </h2>
          
          {/* Bulk Actions */}
          {selectedPages.size > 0 && (
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-600">
                {selectedPages.size} selected
              </span>
              <button
                onClick={handleBulkDelete}
                className="px-3 py-1 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Delete Selected
              </button>
            </div>
          )}
        </div>

        {/* Filters */}
        <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <input
              type="text"
              placeholder="Search pages..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
          
          <div>
            <select
              value={sectionFilter}
              onChange={(e) => handleSectionFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              {sectionOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="createdAt">Sort by Date</option>
              <option value="section">Sort by Section</option>
              <option value="title">Sort by Title</option>
            </select>
          </div>
          
          <div>
            <select
              value={sortOrder}
              onChange={(e) => setSortOrder(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="desc">Descending</option>
              <option value="asc">Ascending</option>
            </select>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-4">
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600">{error}</p>
            <button
              onClick={fetchPages}
              className="mt-2 text-red-600 underline hover:no-underline text-sm"
            >
              Try Again
            </button>
          </div>
        )}

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading pages...</span>
          </div>
        ) : pages.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No pages found.</p>
          </div>
        ) : (
          <>
            {/* Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedPages.size === pages.length && pages.length > 0}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('section')}
                    >
                      Section
                      {sortBy === 'section' && (
                        <span className="ml-1">
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('title')}
                    >
                      Title
                      {sortBy === 'title' && (
                        <span className="ml-1">
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Content
                    </th>
                    <th 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('createdAt')}
                    >
                      Created
                      {sortBy === 'createdAt' && (
                        <span className="ml-1">
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {pages.map((page) => (
                    <PageRow
                      key={page._id}
                      page={page}
                      isSelected={selectedPages.has(page._id)}
                      onSelect={handlePageSelect}
                      onEdit={onEdit}
                      onDelete={handleDelete}
                      formatSectionName={formatSectionName}
                      formatDate={formatDate}
                    />
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <PaginationControls
                pagination={pagination}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
});

// Individual Page Row Component
const PageRow = React.memo(({
  page,
  isSelected,
  onSelect,
  onEdit,
  onDelete,
  formatSectionName,
  formatDate
}) => {
  const handleSelect = useCallback((e) => {
    onSelect(page._id, e.target.checked);
  }, [page._id, onSelect]);

  const handleEdit = useCallback(() => {
    onEdit(page);
  }, [page, onEdit]);

  const handleDelete = useCallback(() => {
    onDelete(page);
  }, [page, onDelete]);

  // Get content summary based on section
  const getContentSummary = useCallback(() => {
    if (page.section === 'testimonials') {
      return `${page.testimonials?.length || 0} testimonials`;
    } else {
      const secondaryCount = page.secondaryEntries?.length || 0;
      return secondaryCount > 0
        ? `Main content + ${secondaryCount} additional entries`
        : 'Main content only';
    }
  }, [page]);

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={handleSelect}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {formatSectionName(page.section)}
          </span>
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm font-medium text-gray-900">
          {page.title || 'No title'}
        </div>
        {page.url && (
          <div className="text-sm text-blue-600 hover:text-blue-800">
            <a href={page.url} target="_blank" rel="noopener noreferrer">
              {page.url.length > 50 ? page.url.substring(0, 50) + '...' : page.url}
            </a>
          </div>
        )}
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-gray-900">
          {getContentSummary()}
        </div>
        {page.body && (
          <div className="text-sm text-gray-500 mt-1">
            {page.body.length > 100 ? page.body.substring(0, 100) + '...' : page.body}
          </div>
        )}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {formatDate(page.createdAt)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <div className="flex space-x-2">
          <button
            onClick={handleEdit}
            className="text-blue-600 hover:text-blue-900"
          >
            Edit
          </button>
          <button
            onClick={handleDelete}
            className="text-red-600 hover:text-red-900"
          >
            Delete
          </button>
        </div>
      </td>
    </tr>
  );
});

// Pagination Controls Component
const PaginationControls = React.memo(({ pagination, currentPage, onPageChange }) => {
  const { totalPages, hasNextPage, hasPrevPage } = pagination;

  const handlePrevious = useCallback(() => {
    if (hasPrevPage) {
      onPageChange(currentPage - 1);
    }
  }, [hasPrevPage, currentPage, onPageChange]);

  const handleNext = useCallback(() => {
    if (hasNextPage) {
      onPageChange(currentPage + 1);
    }
  }, [hasNextPage, currentPage, onPageChange]);

  const handlePageClick = useCallback((page) => {
    onPageChange(page);
  }, [onPageChange]);

  // Generate page numbers to show
  const getPageNumbers = useMemo(() => {
    const pages = [];
    const maxVisible = 5;
    let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(totalPages, start + maxVisible - 1);

    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }, [currentPage, totalPages]);

  return (
    <div className="mt-6 flex items-center justify-between">
      <div className="text-sm text-gray-700">
        Showing page {currentPage} of {totalPages}
      </div>

      <div className="flex items-center space-x-2">
        <button
          onClick={handlePrevious}
          disabled={!hasPrevPage}
          className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>

        {getPageNumbers.map(page => (
          <button
            key={page}
            onClick={() => handlePageClick(page)}
            className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
              page === currentPage
                ? 'text-white bg-blue-600 border border-blue-600'
                : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
            }`}
          >
            {page}
          </button>
        ))}

        <button
          onClick={handleNext}
          disabled={!hasNextPage}
          className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
        </button>
      </div>
    </div>
  );
});

PageRow.displayName = 'PageRow';
PaginationControls.displayName = 'PaginationControls';
PagesList.displayName = 'PagesList';

export default PagesList;
