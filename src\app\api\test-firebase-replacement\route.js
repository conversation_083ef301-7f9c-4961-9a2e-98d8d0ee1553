import { NextResponse } from 'next/server';
import { uploadFileServer, deleteFileByUrlServer } from '@/lib/server-file-upload';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';

// POST /api/test-firebase-replacement - Test Firebase file replacement workflow
export async function POST(request) {
  try {
    console.log('Testing Firebase file replacement workflow...');
    
    const results = {
      step1_initialUpload: null,
      step2_databaseCreate: null,
      step3_replacementUpload: null,
      step4_fileDelete: null,
      step5_databaseUpdate: null,
      step6_verification: null
    };
    
    // Step 1: Create initial test file and upload
    console.log('Step 1: Creating and uploading initial test file...');
    const initialContent = 'Initial 360° test content for replacement workflow';
    const initialFile = new File([initialContent], 'test-replacement-360.jpg', {
      type: 'image/jpeg',
      lastModified: Date.now()
    });
    
    const initialUpload = await uploadFileServer(initialFile, '360s', 'test-replacement-initial.jpg');
    results.step1_initialUpload = {
      success: initialUpload.success,
      url: initialUpload.url?.substring(0, 100) + '...',
      isFirebaseURL: initialUpload.url?.includes('firebasestorage.googleapis.com'),
      storageType: initialUpload.storage
    };
    
    if (!initialUpload.success) {
      throw new Error('Initial upload failed');
    }
    
    // Step 2: Create database record with initial file
    console.log('Step 2: Creating database record...');
    await connectDB();
    
    const testData = {
      name: 'test_replacement_360',
      url: initialUpload.url,
      originalFileName: 'test-replacement-initial.jpg',
      priority: 999,
      markerList: [{
        name: 'test_marker',
        markerType: 'guide',
        x: 10,
        y: 20,
        z: 30,
        _360Name: '',
        id: 'test-marker-1'
      }],
      cameraPosition: 1.5,
      _360Rotation: 2.5
    };
    
    const new360 = new _360Settings(testData);
    const saved360 = await new360.save();
    
    results.step2_databaseCreate = {
      success: true,
      recordId: saved360._id.toString(),
      hasFirebaseURL: saved360.url.includes('firebasestorage.googleapis.com'),
      hasMarkers: saved360.markerList.length > 0,
      hasCameraSettings: saved360.cameraPosition !== -0.0001
    };
    
    // Step 3: Create replacement file and upload
    console.log('Step 3: Creating and uploading replacement file...');
    const replacementContent = 'Replacement 360° test content for replacement workflow';
    const replacementFile = new File([replacementContent], 'test-replacement-360-new.jpg', {
      type: 'image/jpeg',
      lastModified: Date.now()
    });
    
    const replacementUpload = await uploadFileServer(replacementFile, '360s', 'test-replacement-new.jpg');
    results.step3_replacementUpload = {
      success: replacementUpload.success,
      url: replacementUpload.url?.substring(0, 100) + '...',
      isFirebaseURL: replacementUpload.url?.includes('firebasestorage.googleapis.com'),
      storageType: replacementUpload.storage
    };
    
    if (!replacementUpload.success) {
      throw new Error('Replacement upload failed');
    }
    
    // Step 4: Test Firebase file deletion
    console.log('Step 4: Testing Firebase file deletion...');
    const deleteResult = await deleteFileByUrlServer(initialUpload.url);
    results.step4_fileDelete = {
      success: deleteResult.success,
      deletedPath: deleteResult.deletedPath,
      error: deleteResult.error
    };
    
    // Step 5: Update database with new URL
    console.log('Step 5: Updating database with replacement URL...');
    const updatedRecord = await _360Settings.findByIdAndUpdate(
      saved360._id,
      { 
        url: replacementUpload.url,
        originalFileName: 'test-replacement-360-new.jpg'
      },
      { new: true }
    );
    
    results.step5_databaseUpdate = {
      success: !!updatedRecord,
      newUrl: updatedRecord?.url?.substring(0, 100) + '...',
      isFirebaseURL: updatedRecord?.url?.includes('firebasestorage.googleapis.com'),
      markersPreserved: updatedRecord?.markerList?.length > 0,
      cameraSettingsPreserved: updatedRecord?.cameraPosition !== -0.0001
    };
    
    // Step 6: Verification
    console.log('Step 6: Final verification...');
    const finalRecord = await _360Settings.findById(saved360._id);
    
    results.step6_verification = {
      urlChanged: finalRecord.url !== initialUpload.url,
      isNewFirebaseURL: finalRecord.url.includes('firebasestorage.googleapis.com'),
      markersIntact: finalRecord.markerList.length === testData.markerList.length,
      cameraSettingsIntact: finalRecord.cameraPosition === testData.cameraPosition,
      rotationSettingsIntact: finalRecord._360Rotation === testData._360Rotation
    };
    
    // Cleanup
    await _360Settings.findByIdAndDelete(saved360._id);
    
    // Try to delete replacement file (cleanup)
    try {
      await deleteFileByUrlServer(replacementUpload.url);
    } catch (cleanupError) {
      console.warn('Cleanup deletion failed:', cleanupError);
    }
    
    console.log('Firebase replacement workflow test completed');
    
    // Overall analysis
    const workflowSuccess = results.step1_initialUpload.success &&
                           results.step2_databaseCreate.success &&
                           results.step3_replacementUpload.success &&
                           results.step5_databaseUpdate.success &&
                           results.step6_verification.urlChanged &&
                           results.step6_verification.markersIntact &&
                           results.step6_verification.cameraSettingsIntact;
    
    return NextResponse.json({
      success: true,
      message: 'Firebase file replacement workflow test completed',
      workflowSuccess,
      results,
      analysis: {
        overallStatus: workflowSuccess ? 'SUCCESS' : 'PARTIAL_SUCCESS',
        firebaseUpload: results.step1_initialUpload.isFirebaseURL ? 'WORKING' : 'USING_MOCK',
        fileReplacement: results.step6_verification.urlChanged ? 'WORKING' : 'FAILED',
        dataPreservation: (results.step6_verification.markersIntact && 
                          results.step6_verification.cameraSettingsIntact) ? 'WORKING' : 'FAILED',
        fileDeletion: results.step4_fileDelete.success ? 'WORKING' : 'FAILED'
      },
      recommendations: [
        workflowSuccess ? 'Firebase replacement workflow is working correctly' : 'Some steps failed - check individual results',
        results.step1_initialUpload.isFirebaseURL ? 'Using real Firebase URLs' : 'Using mock Firebase URLs - configure real Firebase for production',
        'Test the workflow in admin interface by editing existing 360° record with Firebase URL'
      ]
    });
  } catch (error) {
    console.error('Firebase replacement workflow test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack,
      troubleshooting: {
        possibleCauses: [
          'Firebase configuration issue',
          'Database connection problem',
          'File upload/deletion error',
          'URL format validation error'
        ],
        nextSteps: [
          'Check Firebase environment variables',
          'Verify MongoDB connection',
          'Test individual upload/delete operations',
          'Check server logs for detailed errors'
        ]
      }
    }, { status: 500 });
  }
}

// GET /api/test-firebase-replacement - Show test information
export async function GET(request) {
  return NextResponse.json({
    message: 'Use POST method to test Firebase file replacement workflow',
    endpoint: '/api/test-firebase-replacement',
    method: 'POST',
    description: 'Tests the complete Firebase file replacement workflow including upload, deletion, and database updates',
    steps: [
      '1. Upload initial test file to Firebase',
      '2. Create database record with Firebase URL',
      '3. Upload replacement file to Firebase',
      '4. Delete original file from Firebase',
      '5. Update database with new Firebase URL',
      '6. Verify data preservation and URL change'
    ]
  });
}
