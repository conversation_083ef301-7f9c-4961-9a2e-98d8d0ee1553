  'use client'

import Image from "next/image"
import ImageScalerComponent from "../ImageScalerComponent"
import LandingpageComponent from "./LandingpageComponent"
import { useContextExperience } from "@/contexts/useContextExperience"
import { ACTIONS_EXPERIENCE_STATE } from "@/contexts/reducerExperience"
import { useEffect } from "react"

export default function LandinPageImage() {  
  const {disptachExperience}=useContextExperience()

  useEffect(() => {
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.RESET})
  }, [])
  
  // console.log('LandinPageImage:',dataUpdate)
  return (
    <>
      <div className="flex h-full w-full overflow-hidden">
        <ImageScalerComponent 
          style={'object-cover md:w-full w-auto h-full'} 
          src={'/assets/hero_image_001.jpg'} 
          alt='landingpage backround image'/>
      </div>
      <LandingpageComponent/>
    </>
  )
}
