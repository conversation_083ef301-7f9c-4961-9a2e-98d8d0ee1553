'use client';

import { useState, useEffect } from 'react';
import ClientList from './ClientList';
import ClientProfile from './ClientProfile';
import ClientFilters from './ClientFilters';
import ClientStats from './ClientStats';
import CreateClientModal from './CreateClientModal';

export default function ClientManagementDashboard() {
  const [clients, setClients] = useState([]);
  const [selectedClient, setSelectedClient] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [view, setView] = useState('list'); // 'list' | 'profile'
  
  const [filters, setFilters] = useState({
    search: '',
    role: '',
    status: '',
    bookingStatus: '',
    paymentStatus: '',
    location: '',
    sort: '-createdAt',
  });
  
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  });

  useEffect(() => {
    fetchClients();
  }, [filters, pagination.page]);

  const fetchClients = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        includeStats: 'true',
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v)),
      });

      const response = await fetch(`/api/clients?${params}`);
      const data = await response.json();

      if (data.success) {
        setClients(data.data);
        setPagination(prev => ({
          ...prev,
          total: data.pagination.total,
          pages: data.pagination.pages,
        }));
      } else {
        setError(data.message || 'Failed to fetch clients');
      }
    } catch (err) {
      setError('Failed to load clients');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClientSelect = async (client) => {
    setIsLoading(true);
    try {
      // Fetch full client details with bookings and payments
      const response = await fetch(`/api/clients/${client._id}?includeBookings=true&includePayments=true`);
      const data = await response.json();

      if (data.success) {
        setSelectedClient(data.data);
        setView('profile');
      } else {
        setError('Failed to load client details');
      }
    } catch (err) {
      setError('Failed to load client details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClientUpdate = (updatedClient) => {
    // Update client in the list
    setClients(prev => 
      prev.map(client => 
        client._id === updatedClient._id ? updatedClient : client
      )
    );
    
    // Update selected client if it's the same one
    if (selectedClient && selectedClient._id === updatedClient._id) {
      setSelectedClient(updatedClient);
    }
  };

  const handleClientCreate = (newClient) => {
    setClients(prev => [newClient, ...prev]);
    setShowCreateModal(false);
    fetchClients(); // Refresh to get updated stats
  };

  const handleClientDelete = (deletedClientId) => {
    setClients(prev => prev.filter(client => client._id !== deletedClientId));
    if (selectedClient && selectedClient._id === deletedClientId) {
      setSelectedClient(null);
      setView('list');
    }
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handleBackToList = () => {
    setSelectedClient(null);
    setView('list');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {view === 'profile' ? 'Client Profile' : 'Client Management'}
          </h2>
          <p className="text-gray-600">
            {view === 'profile' 
              ? `Viewing details for ${selectedClient?.name}`
              : 'Manage client profiles, bookings, and communications'
            }
          </p>
        </div>
        
        <div className="flex space-x-3">
          {view === 'profile' && (
            <button
              onClick={handleBackToList}
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              ← Back to List
            </button>
          )}
          
          {view === 'list' && (
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Add New Client
            </button>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          {error}
          <button
            onClick={fetchClients}
            className="ml-4 underline hover:no-underline"
          >
            Try Again
          </button>
        </div>
      )}

      {/* Content */}
      {view === 'list' ? (
        <>
          {/* Client Statistics */}
          <ClientStats clients={clients} />

          {/* Filters */}
          <ClientFilters
            filters={filters}
            onFilterChange={handleFilterChange}
            onRefresh={fetchClients}
          />

          {/* Client List */}
          <ClientList
            clients={clients}
            isLoading={isLoading}
            pagination={pagination}
            onClientSelect={handleClientSelect}
            onClientUpdate={handleClientUpdate}
            onClientDelete={handleClientDelete}
            onPageChange={handlePageChange}
            onRefresh={fetchClients}
          />
        </>
      ) : (
        /* Client Profile */
        <ClientProfile
          client={selectedClient}
          isLoading={isLoading}
          onClientUpdate={handleClientUpdate}
          onClientDelete={handleClientDelete}
          onBack={handleBackToList}
        />
      )}

      {/* Create Client Modal */}
      {showCreateModal && (
        <CreateClientModal
          onClientCreate={handleClientCreate}
          onClose={() => setShowCreateModal(false)}
        />
      )}
    </div>
  );
}
