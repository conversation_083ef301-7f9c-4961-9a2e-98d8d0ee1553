'use client';

import { useState } from 'react';
import { MdAdd, MdArrowBack } from 'react-icons/md';
import InfoMarkerForm from './InfoMarkerForm';
import InfoMarkerList from './InfoMarkerList';

export default function InfoMarkerManagement() {
  const [currentView, setCurrentView] = useState('list'); // 'list' | 'create' | 'edit'
  const [selectedInfoMarker, setSelectedInfoMarker] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [notification, setNotification] = useState(null);

  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleCreate = () => {
    setSelectedInfoMarker(null);
    setCurrentView('create');
  };

  const handleEdit = (infoMarker) => {
    setSelectedInfoMarker(infoMarker);
    setCurrentView('edit');
  };

  const handleSave = async (formData) => {
    setIsLoading(true);
    try {
      const url = selectedInfoMarker 
        ? `/api/info-markers/${selectedInfoMarker._id}`
        : '/api/info-markers';
      
      const method = selectedInfoMarker ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        showNotification(
          selectedInfoMarker 
            ? 'Info marker updated successfully' 
            : 'Info marker created successfully'
        );
        setCurrentView('list');
        setRefreshTrigger(prev => prev + 1);
      } else {
        throw new Error(data.message || 'Failed to save info marker');
      }
    } catch (error) {
      console.error('Save error:', error);
      showNotification(error.message, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Are you sure you want to delete this info marker?')) {
      return;
    }

    try {
      const response = await fetch(`/api/info-markers/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        showNotification('Info marker deleted successfully');
        setRefreshTrigger(prev => prev + 1);
      } else {
        throw new Error(data.message || 'Failed to delete info marker');
      }
    } catch (error) {
      console.error('Delete error:', error);
      showNotification(error.message, 'error');
    }
  };

  const handleBulkDelete = async (ids) => {
    try {
      const response = await fetch('/api/info-markers', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      });

      const data = await response.json();

      if (data.success) {
        showNotification(`${data.data.deletedCount} info markers deleted successfully`);
        setRefreshTrigger(prev => prev + 1);
      } else {
        throw new Error(data.message || 'Failed to delete info markers');
      }
    } catch (error) {
      console.error('Bulk delete error:', error);
      showNotification(error.message, 'error');
    }
  };

  const handleCancel = () => {
    setCurrentView('list');
    setSelectedInfoMarker(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {currentView !== 'list' && (
            <button
              onClick={handleCancel}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-md"
            >
              <MdArrowBack className="w-5 h-5" />
            </button>
          )}
          <h1 className="text-2xl font-bold text-gray-900">
            {currentView === 'list' && 'Info Markers Management'}
            {currentView === 'create' && 'Create New Info Marker'}
            {currentView === 'edit' && 'Edit Info Marker'}
          </h1>
        </div>

        {currentView === 'list' && (
          <button
            onClick={handleCreate}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
          >
            <MdAdd className="mr-2" />
            Add Info Marker
          </button>
        )}
      </div>

      {/* Notification */}
      {notification && (
        <div className={`p-4 rounded-md ${
          notification.type === 'success' 
            ? 'bg-green-50 border border-green-200 text-green-700'
            : 'bg-red-50 border border-red-200 text-red-700'
        }`}>
          {notification.message}
        </div>
      )}

      {/* Content */}
      {currentView === 'list' && (
        <InfoMarkerList
          onEdit={handleEdit}
          onDelete={handleDelete}
          onBulkDelete={handleBulkDelete}
          refreshTrigger={refreshTrigger}
        />
      )}

      {(currentView === 'create' || currentView === 'edit') && (
        <InfoMarkerForm
          infoMarker={selectedInfoMarker}
          onSave={handleSave}
          onCancel={handleCancel}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
