# Booking Availability API Schema Registration Fix

## Problem Description

The booking availability API (`/api/bookings/availability`) was experiencing a 500 server error with the message "<PERSON><PERSON><PERSON> hasn't been registered for model 'User'". This error occurred when the API tried to populate customer and package data from related models.

## Root Cause Analysis

The error was caused by missing model imports in the booking availability API route. The API was attempting to use <PERSON>goose's `.populate()` method to fetch related data:

```javascript
// Line 63-64 in the original code
.populate('customer', 'firstname surname name email')
.populate('package', 'name category');
```

However, the API route only imported the `Booking` model:

```javascript
// Original imports (INCOMPLETE)
import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';  // Only Booking model imported
```

When <PERSON><PERSON><PERSON> tried to populate the `customer` field (which references the User model) and the `package` field (which references the Package model), it couldn't find the registered schemas because they weren't imported.

## Solution Implementation

### 1. **Added Missing Model Imports**

**File**: `src/app/api/bookings/availability/route.js`

**Before**:
```javascript
import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
```

**After**:
```javascript
import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
import { User } from '@/models/User';        // ✅ Added User model import
import { Package } from '@/models/Package';  // ✅ Added Package model import
```

### 2. **Enhanced Error Handling**

Added specific error handling for schema registration issues:

```javascript
// Enhanced error handling for schema-related errors
else if (error.message && error.message.includes('Schema hasn\'t been registered')) {
  errorMessage = 'Database schema error. Please contact support.';
  console.error('Schema registration error:', error.message);
} else if (error.message && error.message.includes('MissingSchemaError')) {
  errorMessage = 'Database model error. Please contact support.';
  console.error('Missing schema error:', error.message);
}
```

## Technical Details

### **Model Relationships**

The booking availability API uses the following model relationships:

1. **Booking → User (customer)**:
   ```javascript
   // In Booking model
   customer: {
     type: mongoose.Schema.Types.ObjectId,
     ref: 'User',
     required: true,
   }
   ```

2. **Booking → Package**:
   ```javascript
   // In Booking model
   package: {
     type: mongoose.Schema.Types.ObjectId,
     ref: 'Package',
     required: true,
   }
   ```

### **Populate Operations**

The API performs the following populate operations:

```javascript
const overlappingBookings = await Booking.find({
  // ... query conditions ...
}).populate('customer', 'firstname surname name email')
 .populate('package', 'name category');
```

### **Model Export Pattern**

All models use the standard Mongoose pattern to prevent re-registration errors:

```javascript
// User model
export const User = mongoose.models.User || mongoose.model('User', UserSchema);

// Package model  
export const Package = mongoose.models.Package || mongoose.model('Package', PackageSchema);

// Booking model
export const Booking = mongoose.models.Booking || mongoose.model('Booking', BookingSchema);
```

## Verification Steps

### 1. **API Endpoint Testing**

Test the booking availability API with a GET request:

```
GET /api/bookings/availability?startDate=2025-01-15&endDate=2025-01-20
```

**Expected Response**:
```json
{
  "success": true,
  "data": {
    "startDate": "2025-01-15",
    "endDate": "2025-01-20",
    "isAvailable": true,
    "requestedDates": [...],
    "bookedDates": [...],
    "conflictingBookings": [...]
  }
}
```

### 2. **Error Resolution**

- ✅ No more "Schema hasn't been registered for model 'User'" errors
- ✅ No more "MissingSchemaError" exceptions
- ✅ Populate operations work correctly
- ✅ Customer and package data properly populated in responses

### 3. **Functionality Verification**

- ✅ Date range availability checking works
- ✅ Overlapping booking detection functions correctly
- ✅ Customer information properly populated
- ✅ Package information properly populated
- ✅ Booking status filtering works as expected

## Impact Assessment

### **Before Fix**:
- ❌ Booking availability API returned 500 errors
- ❌ Calendar components couldn't load availability data
- ❌ Booking management dashboard showed errors
- ❌ Date selection functionality was broken

### **After Fix**:
- ✅ Booking availability API returns proper responses
- ✅ Calendar components load availability data correctly
- ✅ Booking management dashboard functions properly
- ✅ Date selection works with real-time availability checking

## Related Files

### **Modified Files**:
- `src/app/api/bookings/availability/route.js` - Added missing model imports and enhanced error handling

### **Referenced Models**:
- `src/models/User.js` - User model with customer information
- `src/models/Package.js` - Package model with booking package details
- `src/models/Booking.js` - Booking model with relationships to User and Package

### **Dependent Components**:
- Booking management dashboard
- Calendar availability checking
- Date selection components
- Booking conflict detection

## Best Practices Applied

1. **Complete Model Imports**: Always import all models that are referenced in populate operations
2. **Error Handling**: Specific error messages for schema-related issues
3. **Mongoose Patterns**: Using standard model export patterns to prevent re-registration
4. **Documentation**: Clear documentation of model relationships and dependencies

## Prevention Measures

To prevent similar issues in the future:

1. **Import Checklist**: When using `.populate()`, ensure all referenced models are imported
2. **Error Monitoring**: Monitor for schema registration errors in production logs
3. **Testing**: Include API endpoint testing in development workflow
4. **Code Review**: Review model imports when adding new populate operations

## Conclusion

The booking availability API schema registration error has been completely resolved by adding the missing User and Package model imports. The API now functions correctly, allowing proper population of customer and package data in booking availability responses. This fix enables the booking management system to work properly with real-time availability checking and conflict detection.
