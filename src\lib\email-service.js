import nodemailer from 'nodemailer';

// Create email transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: process.env.SMTP_PORT || 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

/**
 * Send booking confirmation email
 */
export async function sendBookingConfirmationEmail(booking, customer) {
  try {
    const transporter = createTransporter();
    
    const customerName = customer.firstname && customer.surname 
      ? `${customer.firstname} ${customer.surname}`
      : customer.name || 'Guest';
    
    const checkInDate = new Date(booking.dates.checkIn).toLocaleDateString();
    const checkOutDate = new Date(booking.dates.checkOut).toLocaleDateString();
    const nights = booking.dates.duration || 1;
    
    const mailOptions = {
      from: `"Elephant Island Lodge" <${process.env.SMTP_USER}>`,
      to: customer.email,
      subject: `Booking Confirmation - ${booking.bookingNumber}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
            <h1 style="color: #2c3e50; margin: 0;">Elephant Island Lodge</h1>
            <h2 style="color: #27ae60; margin: 10px 0;">Booking Confirmation</h2>
          </div>
          
          <div style="padding: 20px; background-color: white;">
            <p>Dear ${customerName},</p>
            
            <p>Thank you for your booking! We're excited to welcome you to Elephant Island Lodge.</p>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #2c3e50;">Booking Details</h3>
              <p><strong>Booking Number:</strong> ${booking.bookingNumber}</p>
              <p><strong>Package:</strong> ${booking.package?.name || 'N/A'}</p>
              <p><strong>Check-in:</strong> ${checkInDate}</p>
              <p><strong>Check-out:</strong> ${checkOutDate}</p>
              <p><strong>Number of Nights:</strong> ${nights}</p>
              <p><strong>Guests:</strong> ${booking.guests.adults} adults${booking.guests.children ? `, ${booking.guests.children} children` : ''}</p>
              <p><strong>Total Amount:</strong> $${booking.pricing.totalAmount}</p>
              <p><strong>Status:</strong> ${booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}</p>
            </div>
            
            ${booking.payment.status === 'pending' ? `
              <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #856404;">Payment Required</h3>
                <p>To secure your reservation, please complete your payment within 48 hours.</p>
                <p><a href="${process.env.NEXTAUTH_URL}/payment/${booking._id}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">Complete Payment</a></p>
                <p style="font-size: 12px; color: #6c757d; margin-top: 10px;">
                  <strong>Important:</strong> This reservation is valid for 48 hours. Failure to complete payment will result in the reservation being cancelled.
                </p>
              </div>
            ` : ''}
            
            <p>If you have any questions or need to make changes to your booking, please contact us:</p>
            <ul>
              <li>Email: <EMAIL></li>
              <li>Phone: +****************</li>
            </ul>
            
            <p>We look forward to hosting you!</p>
            
            <p>Best regards,<br>
            The Elephant Island Lodge Team</p>
          </div>
          
          <div style="background-color: #2c3e50; color: white; padding: 15px; text-align: center; font-size: 12px;">
            <p>Elephant Island Lodge | 123 Island Paradise, Tropical Bay | www.elephantislandlodge.com</p>
          </div>
        </div>
      `,
    };
    
    const result = await transporter.sendMail(mailOptions);
    console.log('Booking confirmation email sent:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending booking confirmation email:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Send payment reminder email
 */
export async function sendPaymentReminderEmail(booking, customer) {
  try {
    const transporter = createTransporter();
    
    const customerName = customer.firstname && customer.surname 
      ? `${customer.firstname} ${customer.surname}`
      : customer.name || 'Guest';
    
    const checkInDate = new Date(booking.dates.checkIn).toLocaleDateString();
    const checkOutDate = new Date(booking.dates.checkOut).toLocaleDateString();
    const hoursRemaining = Math.max(0, Math.ceil((Date.now() + 48 * 60 * 60 * 1000 - new Date(booking.createdAt).getTime()) / (1000 * 60 * 60)));
    
    const mailOptions = {
      from: `"Elephant Island Lodge" <${process.env.SMTP_USER}>`,
      to: customer.email,
      subject: `Payment Reminder - Booking ${booking.bookingNumber}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #fff3cd; padding: 20px; text-align: center; border: 1px solid #ffeaa7;">
            <h1 style="color: #856404; margin: 0;">Payment Reminder</h1>
            <h2 style="color: #856404; margin: 10px 0;">Elephant Island Lodge</h2>
          </div>
          
          <div style="padding: 20px; background-color: white;">
            <p>Dear ${customerName},</p>
            
            <p>This is a friendly reminder that your booking payment is still pending.</p>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #2c3e50;">Booking Details</h3>
              <p><strong>Booking Number:</strong> ${booking.bookingNumber}</p>
              <p><strong>Package:</strong> ${booking.package?.name || 'N/A'}</p>
              <p><strong>Check-in:</strong> ${checkInDate}</p>
              <p><strong>Check-out:</strong> ${checkOutDate}</p>
              <p><strong>Total Amount:</strong> $${booking.pricing.totalAmount}</p>
            </div>
            
            <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #721c24;">Action Required</h3>
              <p><strong>Time Remaining:</strong> Approximately ${hoursRemaining} hours</p>
              <p>Please complete your payment to secure your reservation.</p>
              <p><a href="${process.env.NEXTAUTH_URL}/payment/${booking._id}" style="background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Pay Now</a></p>
              <p style="font-size: 14px; color: #721c24; margin-top: 15px;">
                <strong>Important:</strong> This reservation is valid for 48 hours from the time of booking. Failure to complete payment will result in the reservation being cancelled automatically.
              </p>
            </div>
            
            <p>If you're experiencing any issues with payment or have questions, please contact us immediately:</p>
            <ul>
              <li>Email: <EMAIL></li>
              <li>Phone: +****************</li>
            </ul>
            
            <p>We want to ensure your stay with us and are here to help!</p>
            
            <p>Best regards,<br>
            The Elephant Island Lodge Team</p>
          </div>
          
          <div style="background-color: #2c3e50; color: white; padding: 15px; text-align: center; font-size: 12px;">
            <p>Elephant Island Lodge | 123 Island Paradise, Tropical Bay | www.elephantislandlodge.com</p>
          </div>
        </div>
      `,
    };
    
    const result = await transporter.sendMail(mailOptions);
    console.log('Payment reminder email sent:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending payment reminder email:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Send booking cancellation email
 */
export async function sendBookingCancellationEmail(booking, customer, reason = 'Payment not completed within 48 hours') {
  try {
    const transporter = createTransporter();
    
    const customerName = customer.firstname && customer.surname 
      ? `${customer.firstname} ${customer.surname}`
      : customer.name || 'Guest';
    
    const mailOptions = {
      from: `"Elephant Island Lodge" <${process.env.SMTP_USER}>`,
      to: customer.email,
      subject: `Booking Cancelled - ${booking.bookingNumber}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #f8d7da; padding: 20px; text-align: center; border: 1px solid #f5c6cb;">
            <h1 style="color: #721c24; margin: 0;">Booking Cancelled</h1>
            <h2 style="color: #721c24; margin: 10px 0;">Elephant Island Lodge</h2>
          </div>
          
          <div style="padding: 20px; background-color: white;">
            <p>Dear ${customerName},</p>
            
            <p>We regret to inform you that your booking has been cancelled.</p>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #2c3e50;">Cancelled Booking Details</h3>
              <p><strong>Booking Number:</strong> ${booking.bookingNumber}</p>
              <p><strong>Reason:</strong> ${reason}</p>
              <p><strong>Cancellation Date:</strong> ${new Date().toLocaleDateString()}</p>
            </div>
            
            <p>If you would like to make a new reservation, please visit our website or contact us directly.</p>
            
            <p>Contact Information:</p>
            <ul>
              <li>Email: <EMAIL></li>
              <li>Phone: +****************</li>
              <li>Website: www.elephantislandlodge.com</li>
            </ul>
            
            <p>We apologize for any inconvenience and hope to welcome you in the future.</p>
            
            <p>Best regards,<br>
            The Elephant Island Lodge Team</p>
          </div>
          
          <div style="background-color: #2c3e50; color: white; padding: 15px; text-align: center; font-size: 12px;">
            <p>Elephant Island Lodge | 123 Island Paradise, Tropical Bay | www.elephantislandlodge.com</p>
          </div>
        </div>
      `,
    };
    
    const result = await transporter.sendMail(mailOptions);
    console.log('Booking cancellation email sent:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending booking cancellation email:', error);
    return { success: false, error: error.message };
  }
}
