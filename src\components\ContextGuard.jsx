'use client'

import React, { useEffect, useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useMarkerContext } from '@/contexts/MarkerContext'

/**
 * ContextGuard - Ensures components have access to required contexts
 * Provides fallback UI while contexts are loading or unavailable
 */
export function ExperienceContextGuard({ children, fallback = null }) {
  const [isReady, setIsReady] = useState(false)
  const [contextError, setContextError] = useState(null)

  useEffect(() => {
    try {
      // Try to access the context
      const context = useContextExperience()
      if (context && context.experienceState && context.disptachExperience) {
        setIsReady(true)
        setContextError(null)
      } else {
        setContextError('ExperienceContext not properly initialized')
      }
    } catch (error) {
      setContextError(error.message)
      console.warn('ExperienceContextGuard: Context not available', error)
    }
  }, [])

  if (contextError) {
    if (process.env.NODE_ENV === 'development') {
      return (
        <div className="p-4 bg-yellow-100 border border-yellow-400 text-yellow-800 rounded">
          <strong>Context Error:</strong> {contextError}
          <br />
          <small>Component needs to be wrapped with ExperienceContextProvider</small>
        </div>
      )
    }
    return fallback
  }

  if (!isReady) {
    return fallback || (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-600"></div>
      </div>
    )
  }

  return children
}

export function MarkerContextGuard({ children, fallback = null }) {
  const [isReady, setIsReady] = useState(false)
  const [contextError, setContextError] = useState(null)

  useEffect(() => {
    try {
      // Try to access the context
      const context = useMarkerContext()
      if (context && context.markerState && context.setSelectedMarker) {
        setIsReady(true)
        setContextError(null)
      } else {
        setContextError('MarkerContext not properly initialized')
      }
    } catch (error) {
      setContextError(error.message)
      console.warn('MarkerContextGuard: Context not available', error)
    }
  }, [])

  if (contextError) {
    if (process.env.NODE_ENV === 'development') {
      return (
        <div className="p-4 bg-yellow-100 border border-yellow-400 text-yellow-800 rounded">
          <strong>Context Error:</strong> {contextError}
          <br />
          <small>Component needs to be wrapped with MarkerContextProvider</small>
        </div>
      )
    }
    return fallback
  }

  if (!isReady) {
    return fallback || (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-600"></div>
      </div>
    )
  }

  return children
}

/**
 * Combined context guard for components that need both contexts
 */
export function CombinedContextGuard({ children, fallback = null }) {
  return (
    <ExperienceContextGuard fallback={fallback}>
      <MarkerContextGuard fallback={fallback}>
        {children}
      </MarkerContextGuard>
    </ExperienceContextGuard>
  )
}

/**
 * HOC to wrap components with context guards
 */
export function withExperienceContext(Component, fallback = null) {
  return function WrappedComponent(props) {
    return (
      <ExperienceContextGuard fallback={fallback}>
        <Component {...props} />
      </ExperienceContextGuard>
    )
  }
}

export function withMarkerContext(Component, fallback = null) {
  return function WrappedComponent(props) {
    return (
      <MarkerContextGuard fallback={fallback}>
        <Component {...props} />
      </MarkerContextGuard>
    )
  }
}

export function withCombinedContexts(Component, fallback = null) {
  return function WrappedComponent(props) {
    return (
      <CombinedContextGuard fallback={fallback}>
        <Component {...props} />
      </CombinedContextGuard>
    )
  }
}
