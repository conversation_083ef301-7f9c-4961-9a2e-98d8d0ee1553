import { NextResponse } from 'next/server';
import { storage } from '@/lib/firebase';
import { ref, listAll } from 'firebase/storage';

// GET /api/test-firebase - Test Firebase Storage configuration
export async function GET(request) {
  try {
    console.log('Testing Firebase Storage configuration...');

    // Check environment variables with more detail
    const firebaseConfig = {
      apiKey: process.env.FIREBASE_API_KEY ? 'SET' : 'NOT SET',
      authDomain: process.env.FIREBASE_AUTH_DOMAIN ? 'SET' : 'NOT SET',
      projectId: process.env.FIREBASE_PROJECT_ID ? 'SET' : 'NOT SET',
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET ? 'SET' : 'NOT SET',
      messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID ? 'SET' : 'NOT SET',
      appId: process.env.FIREBASE_APP_ID ? 'SET' : 'NOT SET',
    };

    // Show actual values for debugging (first few characters only)
    const configDetails = {
      apiKeyPrefix: process.env.FIREBASE_API_KEY?.substring(0, 10) + '...',
      projectId: process.env.FIREBASE_PROJECT_ID,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
      authDomain: process.env.FIREBASE_AUTH_DOMAIN,
    };

    console.log('Firebase config status:', firebaseConfig);
    console.log('Firebase config details:', configDetails);

    // Test storage access
    const storageRef = ref(storage, 'elephantisland/');
    const result = await listAll(storageRef);

    console.log('Firebase Storage test successful');

    return NextResponse.json({
      success: true,
      message: 'Firebase Storage is working correctly',
      config: firebaseConfig,
      configDetails,
      itemsFound: result.items.length,
      foldersFound: result.prefixes.length,
      testPath: 'elephantisland/'
    });
  } catch (error) {
    console.error('Firebase Storage test failed:', error);

    const configForError = {
      apiKey: process.env.FIREBASE_API_KEY ? 'SET' : 'NOT SET',
      authDomain: process.env.FIREBASE_AUTH_DOMAIN ? 'SET' : 'NOT SET',
      projectId: process.env.FIREBASE_PROJECT_ID ? 'SET' : 'NOT SET',
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET ? 'SET' : 'NOT SET',
      messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID ? 'SET' : 'NOT SET',
      appId: process.env.FIREBASE_APP_ID ? 'SET' : 'NOT SET',
    };

    return NextResponse.json({
      success: false,
      error: error.message,
      code: error.code,
      config: configForError,
      configDetails: {
        apiKeyPrefix: process.env.FIREBASE_API_KEY?.substring(0, 10) + '...',
        projectId: process.env.FIREBASE_PROJECT_ID,
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
      },
      troubleshooting: {
        commonIssues: [
          'Invalid API key or project configuration',
          'Storage bucket does not exist or access denied',
          'Firebase project not properly initialized',
          'Network connectivity issues'
        ]
      }
    }, { status: 500 });
  }
}
