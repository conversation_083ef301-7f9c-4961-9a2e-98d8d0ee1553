'use client';

import { useState, useEffect } from 'react';

export default function ClientCommunications({ clientId }) {
  const [communications, setCommunications] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newCommunication, setNewCommunication] = useState({
    type: 'email',
    direction: 'outbound',
    subject: '',
    content: '',
  });

  useEffect(() => {
    fetchCommunications();
  }, [clientId]);

  const fetchCommunications = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // This would fetch communications from the API
      // For now, we'll simulate with empty data
      setCommunications([]);
    } catch (err) {
      setError('Failed to load communications');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddCommunication = async (e) => {
    e.preventDefault();
    
    try {
      // This would add a new communication via API
      const newComm = {
        id: Date.now(),
        ...newCommunication,
        timestamp: new Date(),
        author: 'Current User', // Would be from session
      };
      
      setCommunications(prev => [newComm, ...prev]);
      setNewCommunication({
        type: 'email',
        direction: 'outbound',
        subject: '',
        content: '',
      });
      setShowAddForm(false);
    } catch (err) {
      setError('Failed to add communication');
    }
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTypeIcon = (type) => {
    const icons = {
      email: '📧',
      phone: '📞',
      sms: '💬',
      note: '📝',
      meeting: '🤝',
    };
    return icons[type] || '📝';
  };

  const getDirectionBadge = (direction) => {
    if (direction === 'inbound') {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Inbound</span>;
    }
    return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Outbound</span>;
  };

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Communications</h3>
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Add Communication
        </button>
      </div>

      {/* Add Communication Form */}
      {showAddForm && (
        <div className="bg-gray-50 p-6 rounded-lg border">
          <h4 className="text-md font-medium text-gray-900 mb-4">Add New Communication</h4>
          <form onSubmit={handleAddCommunication} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                <select
                  value={newCommunication.type}
                  onChange={(e) => setNewCommunication(prev => ({ ...prev, type: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="email">Email</option>
                  <option value="phone">Phone Call</option>
                  <option value="sms">SMS</option>
                  <option value="note">Internal Note</option>
                  <option value="meeting">Meeting</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Direction</label>
                <select
                  value={newCommunication.direction}
                  onChange={(e) => setNewCommunication(prev => ({ ...prev, direction: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="outbound">Outbound (We contacted them)</option>
                  <option value="inbound">Inbound (They contacted us)</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Subject</label>
              <input
                type="text"
                value={newCommunication.subject}
                onChange={(e) => setNewCommunication(prev => ({ ...prev, subject: e.target.value }))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Brief subject or title"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Content</label>
              <textarea
                value={newCommunication.content}
                onChange={(e) => setNewCommunication(prev => ({ ...prev, content: e.target.value }))}
                rows={4}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Communication details, notes, or summary"
                required
              />
            </div>

            <div className="flex space-x-3">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Add Communication
              </button>
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Communications List */}
      {communications.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-500">
            No communications recorded yet. Add the first communication above.
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {communications.map((comm) => (
            <div key={comm.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className="text-lg">{getTypeIcon(comm.type)}</span>
                    <div className="text-lg font-medium text-gray-900">{comm.subject}</div>
                    {getDirectionBadge(comm.direction)}
                  </div>
                  
                  <div className="text-sm text-gray-600 mb-3">
                    {comm.content}
                  </div>
                  
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>By {comm.author}</span>
                    <span>•</span>
                    <span>{formatDate(comm.timestamp)}</span>
                    <span>•</span>
                    <span className="capitalize">{comm.type}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 className="text-md font-medium text-blue-900 mb-3">Quick Actions</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <button className="bg-white border border-blue-200 text-blue-700 px-4 py-2 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
            📧 Send Email
          </button>
          <button className="bg-white border border-blue-200 text-blue-700 px-4 py-2 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
            📞 Schedule Call
          </button>
          <button className="bg-white border border-blue-200 text-blue-700 px-4 py-2 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
            📝 Add Note
          </button>
        </div>
      </div>
    </div>
  );
}
