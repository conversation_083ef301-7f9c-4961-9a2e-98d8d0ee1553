import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';

// GET /api/360s - Get all 360s with search and filtering (public access)
export async function GET(request) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-priority';
    const id = searchParams.get('id'); // For specific 360 lookup by name

    // Build query
    const query = {};

    // If specific ID/name is requested
    if (id) {
      query.$or = [
        { _id: id },
        { name: id },
        { originalFileName: id }
      ];
    }

    // Search by name or originalFileName
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { originalFileName: { $regex: search, $options: 'i' } },
      ];
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const items = await _360Settings.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await _360Settings.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching 360s:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch 360s',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/360s - Create new 360 (no authentication required)
export async function POST(request) {
  try {
    console.log('360° POST request received');

    // Add timeout for database connection
    const dbPromise = connectDB();
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Database connection timeout')), 10000)
    );

    await Promise.race([dbPromise, timeoutPromise]);
    console.log('Database connected successfully');

    const body = await request.json();
    console.log('Request body received:', JSON.stringify(body, null, 2));

    // Validate required fields
    const requiredFields = ['name', 'url'];
    for (const field of requiredFields) {
      if (!body[field]) {
        console.error(`Validation failed: ${field} is required`);
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `${field} is required`,
            receivedData: body,
          },
          { status: 400 }
        );
      }
    }

    // Validate that URL is a Firebase URL (not local upload)
    const url = String(body.url).trim();
    if (!url.startsWith('https://firebasestorage.googleapis.com/')) {
      console.error(`Validation failed: URL must be a Firebase Storage URL, got: ${url}`);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid URL',
          message: 'URL must be a Firebase Storage URL. Local upload URLs are not allowed for 360° images.',
          receivedUrl: url,
          expectedFormat: 'https://firebasestorage.googleapis.com/...',
        },
        { status: 400 }
      );
    }

    console.log('Validation passed, creating new 360°...');

    // Validate URL format before saving
    const urlToSave = String(body.url).trim();
    const isFirebaseURL = urlToSave.includes('firebasestorage.googleapis.com');
    const isLocalURL = urlToSave.startsWith('/uploads/');

    console.log('URL validation for new 360°:', {
      url: urlToSave.substring(0, 100) + '...',
      isFirebaseURL,
      isLocalURL,
      urlLength: urlToSave.length
    });

    if (isLocalURL) {
      console.warn('WARNING: Local URL detected in creation request. This should be a Firebase URL!');
    }

    if (isFirebaseURL) {
      console.log('✅ Firebase URL detected - this is correct');
    }

    // Clean the data before saving with minimal required fields
    const cleanData = {
      name: String(body.name).trim(),
      url: urlToSave,
      originalFileName: String(body.originalFileName || '').trim(),
      priority: Math.max(0, Number(body.priority) || 0),
    };

    // Only add optional fields if they exist
    if (body.markerList && Array.isArray(body.markerList)) {
      cleanData.markerList = body.markerList;
    }
    if (body.cameraPosition !== undefined) {
      cleanData.cameraPosition = Number(body.cameraPosition) || -0.0001;
    }
    if (body._360Rotation !== undefined) {
      cleanData._360Rotation = Number(body._360Rotation) || -0.0001;
    }

    console.log('Clean data for saving:', JSON.stringify(cleanData, null, 2));

    // Create new 360 with timeout
    const savePromise = (async () => {
      const new360 = new _360Settings(cleanData);
      return await new360.save();
    })();

    const saveTimeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Database save timeout')), 15000)
    );

    const new360 = await Promise.race([savePromise, saveTimeoutPromise]);

    console.log('360° created successfully:', new360._id);

    return NextResponse.json(
      {
        success: true,
        data: new360,
        message: '360 created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating 360:', error);
    console.error('Error stack:', error.stack);

    // Handle timeout errors
    if (error.message.includes('timeout')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Timeout Error',
          message: 'Request timed out. Please try again.',
        },
        { status: 408 }
      );
    }

    if (error.name === 'ValidationError') {
      console.error('Mongoose validation error:', error.errors);
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create 360',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// Shared update logic for both PUT and PATCH methods
const handleBulkUpdate = async (request) => {
  try {
    await connectDB();

    const body = await request.json();
    const { items, action } = body;

    if (!Array.isArray(items)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'items must be an array',
        },
        { status: 400 }
      );
    }

    const results = [];

    for (const itemData of items) {
      try {
        const { _id, ...updateData } = itemData;

        // Handle bulk actions
        if (action) {
          switch (action) {
            case 'update_priority':
              // Priority updates handled individually
              break;
            default:
              // Direct field updates
              break;
          }
        }

        const updated360 = await _360Settings.findByIdAndUpdate(
          _id,
          updateData,
          { new: true, runValidators: true }
        );

        if (updated360) {
          results.push({
            id: _id,
            success: true,
            data: updated360,
          });
        } else {
          results.push({
            id: _id,
            success: false,
            error: '360 not found',
          });
        }
      } catch (error) {
        results.push({
          id: itemData._id,
          success: false,
          error: error.message,
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed',
    });
  } catch (error) {
    console.error('Error bulk updating 360s:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update 360s',
        message: error.message,
      },
      { status: 500 }
    );
  }
};

// PUT /api/360s - Bulk update 360s (complete replacement, no authentication required)
export async function PUT(request) {
  return handleBulkUpdate(request);
}

// PATCH /api/360s - Bulk partial update 360s (partial updates like markers, no authentication required)
export async function PATCH(request) {
  return handleBulkUpdate(request);
}

// DELETE /api/360s - Bulk delete 360s (no authentication required)
export async function DELETE(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    const { ids } = body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'ids must be a non-empty array',
        },
        { status: 400 }
      );
    }
    
    const result = await _360Settings.deleteMany({
      _id: { $in: ids }
    });
    
    return NextResponse.json({
      success: true,
      data: {
        deletedCount: result.deletedCount,
        requestedCount: ids.length,
      },
      message: `${result.deletedCount} 360s deleted successfully`
    });
  } catch (error) {
    console.error('Error deleting 360s:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete 360s',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
