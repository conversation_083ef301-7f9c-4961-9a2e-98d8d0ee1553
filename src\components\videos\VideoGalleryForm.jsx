'use client';

import { useState, useEffect } from 'react';
import { MdSave, MdCancel, MdCloudUpload, MdVideoLibrary } from 'react-icons/md';

export default function VideoGalleryForm({ 
  videoGallery = null, 
  onSave, 
  onCancel, 
  isLoading = false 
}) {
  const [formData, setFormData] = useState({
    title: '',
    url: '',
  });
  const [errors, setErrors] = useState({});
  const [videoFile, setVideoFile] = useState(null);
  const [videoPreview, setVideoPreview] = useState('');
  const [uploading, setUploading] = useState(false);

  // Initialize form data when videoGallery prop changes
  useEffect(() => {
    if (videoGallery) {
      setFormData({
        title: videoGallery.title || '',
        url: videoGallery.url || '',
      });
      setVideoPreview(videoGallery.url || '');
    } else {
      setFormData({
        title: '',
        url: '',
      });
      setVideoPreview('');
    }
  }, [videoGallery]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleVideoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setVideoFile(file);
      
      // Create preview URL
      const videoUrl = URL.createObjectURL(file);
      setVideoPreview(videoUrl);
    }
  };

  const uploadVideo = async () => {
    if (!videoFile) return null;
    
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('files', videoFile);
      
      const response = await fetch('/api/upload/video-gallery', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      
      if (result.success && result.data.length > 0) {
        return result.data[0].url;
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Video upload error:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    if (!formData.url && !videoFile) {
      newErrors.url = 'Video is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      let videoUrl = formData.url;
      
      // Upload new video if selected
      if (videoFile) {
        videoUrl = await uploadVideo();
      }
      
      const submitData = {
        ...formData,
        url: videoUrl,
      };
      
      await onSave(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: 'Failed to save video. Please try again.' });
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        {videoGallery ? 'Edit Video Gallery Item' : 'Create New Video Gallery Item'}
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
            Title *
          </label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.title ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter video title"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title}</p>
          )}
        </div>

        {/* Video Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Video *
          </label>
          
          {/* Video Preview */}
          {videoPreview && (
            <div className="mb-4">
              <video
                src={videoPreview}
                controls
                className="w-full max-w-md h-48 object-cover rounded-md border border-gray-300"
              >
                Your browser does not support the video tag.
              </video>
            </div>
          )}
          
          {/* File Input */}
          <div className="flex items-center space-x-4">
            <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
              <MdCloudUpload className="mr-2" />
              Choose Video
              <input
                type="file"
                accept="video/*"
                onChange={handleVideoChange}
                className="hidden"
              />
            </label>
            
            {videoFile && (
              <span className="text-sm text-gray-600">
                {videoFile.name}
              </span>
            )}
          </div>
          
          {errors.url && (
            <p className="mt-1 text-sm text-red-600">{errors.url}</p>
          )}
          
          <p className="mt-1 text-sm text-gray-500">
            Supported formats: MP4, WebM, AVI. Maximum size: 100MB.
          </p>
        </div>

        {/* URL Field (Alternative) */}
        <div>
          <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-2">
            Video URL (Alternative)
          </label>
          <input
            type="url"
            id="url"
            name="url"
            value={formData.url}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="https://example.com/video.mp4"
          />
          <p className="mt-1 text-sm text-gray-500">
            You can either upload a video file or provide a direct URL to the video.
          </p>
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-600 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 flex items-center"
          >
            <MdCancel className="mr-2" />
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isLoading || uploading}
            className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center ${
              isLoading || uploading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <MdSave className="mr-2" />
            {isLoading || uploading ? 'Saving...' : 'Save Video'}
          </button>
        </div>
      </form>
    </div>
  );
}
