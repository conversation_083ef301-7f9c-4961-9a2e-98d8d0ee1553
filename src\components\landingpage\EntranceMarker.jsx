import Image from 'next/image'
import React from 'react'
import ImageScalerComponent from '../ImageScalerComponent'

export default function EntranceMarker() {
  return (
    <div className="flex text-white flex-col items-center cursor-pointer select-none absolute text-center left-0 right-0 bottom-24 mx-auto bg-black/75' justify-end w-[332px] h-[541px] bg-black/25' p-2">
        <div className="flex relative max-w-fit max-h-fit">
        <ImageScalerComponent alt='swipe info icon' src={'/assets/elephant_island_logo.png'}/>
        </div>

        <div className="flex relative items-center  text-center flex-col uppercase mb-4">
        <span className="font-medium text-nowrap text-3xl">your safari holiday</span>
        <span className="font-bold text-5xl">destination</span>
        </div>

        <div className="flex relative max-h-fit max-w-fit rounded-full">
        <ImageScalerComponent alt='swipe info icon' src={'/assets/swipe_icon.png'}/>
        </div>

        <span className="font-bold tracking-tighter text-center text-lg uppercase mb-6">look around and explore</span>
    </div>
  )
}
