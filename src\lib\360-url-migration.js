/**
 * 360° URL Migration Utility
 * Migrates local upload URLs to Firebase URLs for existing 360° records
 */

import { uploadFileFirebaseOnly } from './file-upload';
import fs from 'fs';
import path from 'path';

/**
 * Check if a URL is a local upload URL that needs migration
 * @param {string} url - URL to check
 * @returns {boolean} - Whether the URL needs migration
 */
export function needsMigration(url) {
  if (!url || typeof url !== 'string') {
    return false;
  }

  // Check if it's a local upload URL
  return url.startsWith('/uploads/') || url.startsWith('uploads/');
}

/**
 * Extract filename from local upload URL
 * @param {string} url - Local upload URL
 * @returns {string|null} - Extracted filename or null
 */
export function extractFilenameFromLocalUrl(url) {
  if (!url) return null;

  try {
    // Handle both /uploads/360s/filename.jpg and uploads/360s/filename.jpg
    const cleanUrl = url.startsWith('/') ? url.substring(1) : url;
    const parts = cleanUrl.split('/');
    
    // Should be uploads/360s/filename.jpg
    if (parts.length >= 3 && parts[0] === 'uploads') {
      return parts[parts.length - 1]; // Get the last part (filename)
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting filename from URL:', url, error);
    return null;
  }
}

/**
 * Get the local file path from upload URL
 * @param {string} url - Local upload URL
 * @returns {string|null} - Local file path or null
 */
export function getLocalFilePath(url) {
  const filename = extractFilenameFromLocalUrl(url);
  if (!filename) return null;

  return path.join(process.cwd(), 'public', 'uploads', '360s', filename);
}

/**
 * Check if local file exists
 * @param {string} localPath - Local file path
 * @returns {boolean} - Whether file exists
 */
export function localFileExists(localPath) {
  try {
    return fs.existsSync(localPath);
  } catch (error) {
    console.error('Error checking file existence:', localPath, error);
    return false;
  }
}

/**
 * Create a File object from local file path
 * @param {string} localPath - Local file path
 * @param {string} filename - Original filename
 * @returns {File|null} - File object or null
 */
export function createFileFromLocalPath(localPath, filename) {
  try {
    if (!localFileExists(localPath)) {
      console.error('Local file does not exist:', localPath);
      return null;
    }

    const buffer = fs.readFileSync(localPath);
    const stats = fs.statSync(localPath);
    
    // Determine MIME type based on extension
    const ext = path.extname(filename).toLowerCase();
    let mimeType = 'image/jpeg'; // Default
    
    switch (ext) {
      case '.png':
        mimeType = 'image/png';
        break;
      case '.jpg':
      case '.jpeg':
        mimeType = 'image/jpeg';
        break;
      case '.tiff':
      case '.tif':
        mimeType = 'image/tiff';
        break;
      default:
        mimeType = 'image/jpeg';
    }

    // Create a File-like object
    const file = new File([buffer], filename, {
      type: mimeType,
      lastModified: stats.mtime.getTime(),
    });

    return file;
  } catch (error) {
    console.error('Error creating file from local path:', localPath, error);
    return null;
  }
}

/**
 * Migrate a single 360° record from local URL to Firebase URL
 * @param {Object} threeSixtyRecord - 360° record with local URL
 * @returns {Promise<Object|null>} - Migration result or null if failed
 */
export async function migrateSingle360ToFirebase(threeSixtyRecord) {
  try {
    const { _id, url, name, originalFileName } = threeSixtyRecord;

    console.log(`Starting migration for 360° record: ${name} (${_id})`);

    // Check if migration is needed
    if (!needsMigration(url)) {
      console.log(`No migration needed for ${name}: already has Firebase URL`);
      return {
        success: true,
        migrated: false,
        reason: 'Already has Firebase URL',
        originalUrl: url,
        newUrl: url,
      };
    }

    // Get local file path
    const localPath = getLocalFilePath(url);
    if (!localPath) {
      console.error(`Could not determine local path for ${name}: ${url}`);
      return {
        success: false,
        error: 'Could not determine local file path',
        originalUrl: url,
      };
    }

    // Check if local file exists
    if (!localFileExists(localPath)) {
      console.error(`Local file does not exist for ${name}: ${localPath}`);
      return {
        success: false,
        error: 'Local file does not exist',
        originalUrl: url,
        localPath,
      };
    }

    // Create File object from local file
    const filename = originalFileName || extractFilenameFromLocalUrl(url);
    const file = createFileFromLocalPath(localPath, filename);
    
    if (!file) {
      console.error(`Could not create file object for ${name}`);
      return {
        success: false,
        error: 'Could not create file object',
        originalUrl: url,
        localPath,
      };
    }

    // Upload to Firebase
    console.log(`Uploading ${name} to Firebase...`);
    const uploadResult = await uploadFileFirebaseOnly(file, '360s', filename);

    console.log(`Successfully migrated ${name} to Firebase: ${uploadResult.url}`);

    return {
      success: true,
      migrated: true,
      originalUrl: url,
      newUrl: uploadResult.url,
      firebasePath: uploadResult.path,
      localPath,
    };

  } catch (error) {
    console.error(`Migration failed for ${threeSixtyRecord.name}:`, error);
    return {
      success: false,
      error: error.message,
      originalUrl: threeSixtyRecord.url,
    };
  }
}

/**
 * Generate migration report
 * @param {Array} results - Array of migration results
 * @returns {Object} - Migration summary
 */
export function generateMigrationReport(results) {
  const total = results.length;
  const successful = results.filter(r => r.success).length;
  const migrated = results.filter(r => r.success && r.migrated).length;
  const alreadyMigrated = results.filter(r => r.success && !r.migrated).length;
  const failed = results.filter(r => !r.success).length;

  return {
    total,
    successful,
    migrated,
    alreadyMigrated,
    failed,
    results,
  };
}

/**
 * Clean up local files after successful migration (optional)
 * @param {Array} migrationResults - Results from migration
 * @param {boolean} dryRun - Whether to actually delete files
 * @returns {Object} - Cleanup summary
 */
export function cleanupLocalFiles(migrationResults, dryRun = true) {
  const successfulMigrations = migrationResults.filter(r => r.success && r.migrated);
  const cleanupResults = [];

  for (const result of successfulMigrations) {
    try {
      if (result.localPath && localFileExists(result.localPath)) {
        if (!dryRun) {
          fs.unlinkSync(result.localPath);
          console.log(`Deleted local file: ${result.localPath}`);
        } else {
          console.log(`Would delete local file: ${result.localPath}`);
        }
        
        cleanupResults.push({
          localPath: result.localPath,
          deleted: !dryRun,
          success: true,
        });
      }
    } catch (error) {
      console.error(`Failed to delete local file: ${result.localPath}`, error);
      cleanupResults.push({
        localPath: result.localPath,
        deleted: false,
        success: false,
        error: error.message,
      });
    }
  }

  return {
    total: successfulMigrations.length,
    processed: cleanupResults.length,
    deleted: cleanupResults.filter(r => r.deleted).length,
    failed: cleanupResults.filter(r => !r.success).length,
    dryRun,
    results: cleanupResults,
  };
}
