'use client';

import { useMemo } from 'react';

export default function BookingStats({ bookings, dateRange }) {
  const stats = useMemo(() => {
    if (!bookings || bookings.length === 0) {
      return {
        total: 0,
        confirmed: 0,
        checkedIn: 0,
        checkedOut: 0,
        cancelled: 0,
        pending: 0,
        totalRevenue: 0,
        averageBookingValue: 0,
        occupancyRate: 0,
        upcomingCheckIns: 0,
        upcomingCheckOuts: 0,
        byStatus: {},
        byPackage: {},
        revenueByStatus: {},
      };
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);

    const calculations = {
      total: bookings.length,
      confirmed: 0,
      checkedIn: 0,
      checkedOut: 0,
      cancelled: 0,
      pending: 0,
      totalRevenue: 0,
      upcomingCheckIns: 0,
      upcomingCheckOuts: 0,
      byStatus: {},
      byPackage: {},
      revenueByStatus: {},
    };

    bookings.forEach(booking => {
      const checkInDate = new Date(booking.dates.checkIn);
      const checkOutDate = new Date(booking.dates.checkOut);
      const bookingValue = booking.pricing?.totalAmount || 0;

      // Count by status
      calculations.byStatus[booking.status] = (calculations.byStatus[booking.status] || 0) + 1;
      calculations.revenueByStatus[booking.status] = (calculations.revenueByStatus[booking.status] || 0) + bookingValue;

      // Count by package
      const packageName = booking.package?.name || 'Unknown Package';
      calculations.byPackage[packageName] = (calculations.byPackage[packageName] || 0) + 1;

      // Status counts
      switch (booking.status) {
        case 'confirmed':
          calculations.confirmed++;
          break;
        case 'checked_in':
          calculations.checkedIn++;
          break;
        case 'checked_out':
          calculations.checkedOut++;
          break;
        case 'cancelled':
          calculations.cancelled++;
          break;
        case 'pending':
          calculations.pending++;
          break;
      }

      // Revenue calculation (only for non-cancelled bookings)
      if (booking.status !== 'cancelled') {
        calculations.totalRevenue += bookingValue;
      }

      // Upcoming check-ins (today and tomorrow)
      if (checkInDate >= today && checkInDate < tomorrow && booking.status === 'confirmed') {
        calculations.upcomingCheckIns++;
      }

      // Upcoming check-outs (today and tomorrow)
      if (checkOutDate >= today && checkOutDate < tomorrow && booking.status === 'checked_in') {
        calculations.upcomingCheckOuts++;
      }
    });

    // Calculate averages
    calculations.averageBookingValue = calculations.total > 0 
      ? calculations.totalRevenue / (calculations.total - calculations.cancelled)
      : 0;

    // Calculate occupancy rate (simplified - based on confirmed + checked_in vs total capacity)
    const activeBookings = calculations.confirmed + calculations.checkedIn;
    calculations.occupancyRate = calculations.total > 0 
      ? (activeBookings / calculations.total) * 100 
      : 0;

    return calculations;
  }, [bookings]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'bg-yellow-500',
      confirmed: 'bg-blue-500',
      checked_in: 'bg-green-500',
      checked_out: 'bg-gray-500',
      cancelled: 'bg-red-500',
      no_show: 'bg-purple-500',
    };
    return colors[status] || 'bg-gray-400';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Bookings */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Total Bookings</dt>
              <dd className="text-lg font-medium text-gray-900">{formatNumber(stats.total)}</dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-blue-600 font-medium">{stats.confirmed}</span>
            <span className="text-gray-500 ml-1">confirmed</span>
            <span className="text-gray-400 mx-2">•</span>
            <span className="text-yellow-600 font-medium">{stats.pending}</span>
            <span className="text-gray-500 ml-1">pending</span>
          </div>
        </div>
      </div>

      {/* Total Revenue */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
              <dd className="text-lg font-medium text-gray-900">{formatCurrency(stats.totalRevenue)}</dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-gray-600">
              {formatCurrency(stats.averageBookingValue)} avg booking
            </span>
          </div>
        </div>
      </div>

      {/* Occupancy Rate */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Occupancy Rate</dt>
              <dd className="text-lg font-medium text-gray-900">{stats.occupancyRate.toFixed(1)}%</dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-green-600 font-medium">{stats.checkedIn}</span>
            <span className="text-gray-500 ml-1">currently in-house</span>
          </div>
        </div>
      </div>

      {/* Today's Activity */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Today's Activity</dt>
              <dd className="text-lg font-medium text-gray-900">
                {stats.upcomingCheckIns + stats.upcomingCheckOuts}
              </dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-green-600 font-medium">{stats.upcomingCheckIns}</span>
            <span className="text-gray-500 ml-1">check-ins</span>
            <span className="text-gray-400 mx-2">•</span>
            <span className="text-blue-600 font-medium">{stats.upcomingCheckOuts}</span>
            <span className="text-gray-500 ml-1">check-outs</span>
          </div>
        </div>
      </div>

      {/* Status Breakdown */}
      <div className="bg-white shadow rounded-lg p-6 md:col-span-2">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Booking Status Breakdown</h3>
        <div className="space-y-3">
          {Object.entries(stats.byStatus).map(([status, count]) => (
            <div key={status} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full ${getStatusColor(status)} mr-3`}></div>
                <span className="text-sm font-medium text-gray-900 capitalize">
                  {status.replace('_', ' ')}
                </span>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium text-gray-900">{count}</span>
                <span className="text-sm text-gray-500">
                  {formatCurrency(stats.revenueByStatus[status] || 0)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Top Packages */}
      <div className="bg-white shadow rounded-lg p-6 md:col-span-2">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Popular Packages</h3>
        {Object.keys(stats.byPackage).length > 0 ? (
          <div className="space-y-3">
            {Object.entries(stats.byPackage)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 5)
              .map(([packageName, count], index) => (
                <div key={packageName} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3">
                      {index + 1}
                    </div>
                    <span className="text-sm font-medium text-gray-900">{packageName}</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{count} bookings</span>
                </div>
              ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-4">
            No booking data available
          </div>
        )}
      </div>
    </div>
  );
}
