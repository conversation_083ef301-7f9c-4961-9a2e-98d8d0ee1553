'use client';

import { settings } from '@/lib/settings';
import { useState, useEffect } from 'react';

function MenuInputFeilds({item, _360Files, value, onChange, category, index}) {
  const handleSelectChange = (e) => {
    onChange(category, index, e.target.value);
  };

  return(
    <div className='flex w-full items-center h-14 gap-2 p-2'>
      <div className='flex w-full items-center h-14 gap-2 shadow border-1 justify-between border-gray-200 p-2 rounded-md'>
        <span className='text-sm capitalize'>{item?.name}: select 360 link</span>
        <select
          className='flex h-full items-center bg-gray-100 outline-1 rounded-md px-2 text-xs'
          value={value || ''}
          onChange={handleSelectChange}
        >
          <option value="">Select 360° link</option>
          {_360Files.map((file, fileIndex) => (
            <option
              className='text-center max-w-fit text-xs capitalize'
              key={file._id || fileIndex}
              value={file?.name}
            >
              {file?.name}
            </option>
          ))}
        </select>
      </div>
    </div>
  )
}

export default function SettingsManagementDashboard() {
  const [_360Files, set_360Files] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [siteSettings, setSiteSettings] = useState(null);
  const [menuLinks, setMenuLinks] = useState({
    home: [],
    entrance: [],
    firstFloor: [],
    outDoors: [],
    campOutskirts: []
  });

  useEffect(() => {
    fetch360Files();
    fetchSiteSettings();
  }, [])
  
  const fetch360Files = async () => {
    try {
      const response = await fetch(`/api/360s`);
      const data = await response.json();

      if (data.success) {
        set_360Files(data.data);
      } else {
        setError(data.message || 'Failed to fetch 360° images');
      }
    } catch (error) {
      console.error('Error fetching 360 files:', error);
      setError('Failed to load 360° images');
    }
  };

  const fetchSiteSettings = async () => {
    try {
      const response = await fetch('/api/site-management');
      const data = await response.json();

      if (data.success) {
        setSiteSettings(data.data);
        setMenuLinks(data.data.menulinks || {
          home: [],
          entrance: [],
          firstFloor: [],
          outDoors: [],
          campOutskirts: []
        });
        setIsLoading(false);
      } else {
        setError(data.message || 'Failed to fetch site settings');
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error fetching site settings:', error);
      setError('Failed to load site settings');
      setIsLoading(false);
    }
  };
  
  const handleMenuLinkChange = (category, index, value) => {
    setMenuLinks(prev => {
      const updated = { ...prev };
      if (!updated[category]) {
        updated[category] = [];
      }

      // Ensure the array has enough elements
      while (updated[category].length <= index) {
        updated[category].push('');
      }

      updated[category][index] = value;
      return updated;
    });
  };

  const handleSave = async () => {
    setIsSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/site-management', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          menulinks: menuLinks
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Site settings saved successfully!');
        setSiteSettings(data.data);

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(data.message || 'Failed to save site settings');
      }
    } catch (error) {
      console.error('Error saving site settings:', error);
      setError('Failed to save site settings');
    } finally {
      setIsSaving(false);
    }
  };
  
  // Create new functionality removed - only predefined packages can be edited

  return (
    <div className="SettingsManagementDashboard space-y-6 h-full">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className='flex flex-col w-full'>
          <h2 className="text-2xl font-bold text-gray-900">
            Setting Management
          </h2>
          <div className='flex w-full justify-between items-center'>
            <p className="text-gray-600">
              Manage the site settings
            </p>
            <button
              type="button"
              onClick={handleSave}
              disabled={isLoading || isSaving}
              className="bg-blue-600 w-fit px-4 border-2 text-white py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSaving ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Saving...</span>
                </div>
              ) : (
                'Save Changes'
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Error and Success Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-800">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* Create Package Modal removed - only predefined packages can be edited */}
      <form className='flex flex-col w-full h-full overflow-y-auto'>
        <span className='flex font-medium text-xl capitalize'>menu link asignments</span>
        <div className='flex flex-col gap-2 h-full'>
          <span>asign 360 links to menu buttons</span>
          <div className='relative mt-2 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 h-fit'>
            {settings.menuPopup.home.map((i,index)=>
              <MenuInputFeilds
                item={i}
                _360Files={_360Files}
                value={menuLinks.home[index] || ''}
                onChange={handleMenuLinkChange}
                category="home"
                index={index}
                key={index}
              />
            )}
            {settings.menuPopup.entrance.map((i,index)=>
              <MenuInputFeilds
                item={i}
                _360Files={_360Files}
                value={menuLinks.entrance[index] || ''}
                onChange={handleMenuLinkChange}
                category="entrance"
                index={index}
                key={index}
              />
            )}
            {settings.menuPopup.firstFloor.map((i,index)=>
              <MenuInputFeilds
                item={i}
                _360Files={_360Files}
                value={menuLinks.firstFloor[index] || ''}
                onChange={handleMenuLinkChange}
                category="firstFloor"
                index={index}
                key={index}
              />
            )}
            {settings.menuPopup.outDoors.map((i,index)=>
              <MenuInputFeilds
                item={i}
                _360Files={_360Files}
                value={menuLinks.outDoors[index] || ''}
                onChange={handleMenuLinkChange}
                category="outDoors"
                index={index}
                key={index}
              />
            )}
            {settings.menuPopup.campOutskirts.map((i,index)=>
              <MenuInputFeilds
                item={i}
                _360Files={_360Files}
                value={menuLinks.campOutskirts[index] || ''}
                onChange={handleMenuLinkChange}
                category="campOutskirts"
                index={index}
                key={index}
              />
            )}
          </div>
          
        </div>
      </form>
    </div>
  );
}
