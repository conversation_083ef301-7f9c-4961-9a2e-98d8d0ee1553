'use client'

import { useEffect } from 'react'

/**
 * MemoryCleanup - Component to handle memory cleanup and performance optimization
 * This component runs cleanup tasks and memory management in the background
 */
export default function MemoryCleanup() {
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return

    let cleanupInterval

    const performCleanup = () => {
      try {
        // Force garbage collection if available (Chrome DevTools)
        if (window.gc && process.env.NODE_ENV === 'development') {
          window.gc()
        }

        // Clear any orphaned event listeners
        const orphanedElements = document.querySelectorAll('[data-cleanup="true"]')
        orphanedElements.forEach(element => {
          element.remove()
        })

        // Clean up any Three.js objects that might be lingering
        if (window.THREE) {
          // This is a placeholder for Three.js cleanup
          // Actual cleanup would depend on specific Three.js usage
        }

        // Log memory usage in development
        if (process.env.NODE_ENV === 'development' && performance.memory) {
          const memInfo = performance.memory
          const usedMB = Math.round(memInfo.usedJSHeapSize / 1048576)
          const totalMB = Math.round(memInfo.totalJSHeapSize / 1048576)
          
          // Only log if memory usage is high
          if (usedMB > 100) {
            console.log(`Memory usage: ${usedMB}MB / ${totalMB}MB`)
          }
        }
      } catch (error) {
        console.error('Error during memory cleanup:', error)
      }
    }

    // Run cleanup every 30 seconds
    cleanupInterval = setInterval(performCleanup, 30000)

    // Run initial cleanup after 5 seconds
    const initialCleanup = setTimeout(performCleanup, 5000)

    // Cleanup on page visibility change
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        performCleanup()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Cleanup on beforeunload
    const handleBeforeUnload = () => {
      performCleanup()
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      clearInterval(cleanupInterval)
      clearTimeout(initialCleanup)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [])

  // This component doesn't render anything
  return null
}
