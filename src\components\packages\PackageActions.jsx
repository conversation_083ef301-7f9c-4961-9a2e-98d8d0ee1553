'use client';

import { useState } from 'react';

export default function PackageActions({ 
  package: pkg, 
  onPackageUpdate, 
  onPackageDelete, 
  onPackageSelect 
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  const handleAction = async (action, data = {}) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/packages/${pkg._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, ...data }),
      });

      const result = await response.json();
      
      if (result.success) {
        onPackageUpdate(result.data);
        setShowDropdown(false);
      } else {
        alert(result.message || 'Action failed');
      }
    } catch (error) {
      alert('Failed to perform action');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete "${pkg.name}"? This action cannot be undone.`)) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/packages/${pkg._id}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      
      if (result.success) {
        onPackageDelete(pkg._id);
      } else {
        alert(result.message || 'Failed to delete package');
      }
    } catch (error) {
      alert('Failed to delete package');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = () => {
    const newStatus = !pkg.availability?.isActive;
    if (confirm(`${newStatus ? 'Activate' : 'Deactivate'} "${pkg.name}"?`)) {
      handleAction('toggle_active', { isActive: newStatus });
    }
  };

  const handleToggleFeatured = () => {
    const newStatus = !pkg.featured;
    if (confirm(`${newStatus ? 'Feature' : 'Unfeature'} "${pkg.name}"?`)) {
      handleAction('toggle_featured', { featured: newStatus });
    }
  };

  const handleDuplicate = () => {
    if (confirm(`Create a copy of "${pkg.name}"?`)) {
      handleAction('duplicate');
    }
  };

  return (
    <div className="relative">
      <div className="flex items-center space-x-2">
        {/* Quick Edit Button */}
        <button
          onClick={() => onPackageSelect(pkg)}
          className="text-blue-600 hover:text-blue-900 text-sm"
          disabled={isLoading}
        >
          Edit
        </button>

        {/* Quick Actions Dropdown */}
        <div className="relative">
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            disabled={isLoading}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>

          {showDropdown && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
              <div className="py-1">
                {/* Status Actions */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                  Status
                </div>
                
                <button
                  onClick={handleToggleActive}
                  className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-50 ${
                    pkg.availability?.isActive 
                      ? 'text-orange-700' 
                      : 'text-green-700'
                  }`}
                >
                  {pkg.availability?.isActive ? 'Deactivate' : 'Activate'} Package
                </button>

                <button
                  onClick={handleToggleFeatured}
                  className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-50 ${
                    pkg.featured 
                      ? 'text-orange-700' 
                      : 'text-yellow-700'
                  }`}
                >
                  {pkg.featured ? 'Unfeature' : 'Feature'} Package
                </button>

                {/* Management Actions */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Management
                </div>
                
                <button
                  onClick={handleDuplicate}
                  className="block w-full text-left px-4 py-2 text-sm text-blue-700 hover:bg-blue-50"
                >
                  Duplicate Package
                </button>

                <button
                  onClick={() => {
                    navigator.clipboard.writeText(`${window.location.origin}/packages/${pkg.slug}`);
                    alert('Package URL copied to clipboard!');
                    setShowDropdown(false);
                  }}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Copy Package URL
                </button>

                {/* Analytics */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Analytics
                </div>
                
                <button
                  onClick={() => {
                    // This would open analytics view
                    alert('Analytics view coming soon!');
                    setShowDropdown(false);
                  }}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  View Analytics
                </button>

                <button
                  onClick={() => {
                    // This would show booking history
                    alert('Booking history view coming soon!');
                    setShowDropdown(false);
                  }}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  View Bookings
                </button>

                {/* Dangerous Actions */}
                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-t">
                  Danger Zone
                </div>
                
                <button
                  onClick={handleDelete}
                  className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                >
                  Delete Package
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
}
