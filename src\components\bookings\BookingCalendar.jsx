'use client';

import { useState, useMemo, useEffect, useCallback } from 'react';

export default function BookingCalendar({
  bookings,
  isLoading,
  dateRange,
  onDateRangeChange,
  onBookingSelect,
  onRefresh,
}) {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [viewMode, setViewMode] = useState('month'); // 'month' | 'week'
  const [availabilityData, setAvailabilityData] = useState({});
  const [packages, setPackages] = useState([]);
  const [selectedPackage, setSelectedPackage] = useState('all');
  const [isLoadingAvailability, setIsLoadingAvailability] = useState(false);
  const [hoveredDate, setHoveredDate] = useState(null);
  const [selectedDateRange, setSelectedDateRange] = useState({ start: null, end: null });
  const [showBookingModal, setShowBookingModal] = useState(false);

  // Fetch packages on component mount
  useEffect(() => {
    fetchPackages();
  }, []);

  // Fetch availability data when month or package changes
  useEffect(() => {
    fetchAvailabilityData();
  }, [currentMonth, selectedPackage]);

  // Fetch packages
  const fetchPackages = useCallback(async () => {
    try {
      const response = await fetch('/api/packages');
      const data = await response.json();
      if (data.success) {
        setPackages(data.data);
      }
    } catch (error) {
      console.error('Error fetching packages:', error);
    }
  }, []);

  // Fetch availability data for the current month
  const fetchAvailabilityData = useCallback(async () => {
    setIsLoadingAvailability(true);
    try {
      const year = currentMonth.getFullYear();
      const month = currentMonth.getMonth();

      // Get first and last day of the month view (including prev/next month days)
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const startDate = new Date(firstDay);
      startDate.setDate(startDate.getDate() - firstDay.getDay());
      const endDate = new Date(lastDay);
      endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));

      // Fetch availability for the entire month range
      const response = await fetch(
        `/api/bookings/availability?startDate=${startDate.toISOString().split('T')[0]}&endDate=${endDate.toISOString().split('T')[0]}`
      );
      const data = await response.json();

      if (data.success) {
        // Transform the API response to match our calendar needs
        const availabilityMap = {};

        if (data.data.requestedDates) {
          data.data.requestedDates.forEach(dateInfo => {
            availabilityMap[dateInfo.date] = {
              date: dateInfo.date,
              isAvailable: dateInfo.available,
              booking: dateInfo.booking,
              conflictingBookings: dateInfo.booking ? [dateInfo.booking] : []
            };
          });
        }

        setAvailabilityData(availabilityMap);
      }
    } catch (error) {
      console.error('Error fetching availability data:', error);
    } finally {
      setIsLoadingAvailability(false);
    }
  }, [currentMonth, selectedPackage]);

  // Handle date selection for booking creation
  const handleDateClick = useCallback((date) => {
    const dateStr = date.toISOString().split('T')[0];
    const availability = availabilityData[dateStr];

    // Don't allow selection of unavailable dates
    if (availability && !availability.isAvailable) {
      return;
    }

    if (!selectedDateRange.start || (selectedDateRange.start && selectedDateRange.end)) {
      // Start new selection
      setSelectedDateRange({ start: date, end: null });
    } else if (date > selectedDateRange.start) {
      // Complete the range
      setSelectedDateRange(prev => ({ ...prev, end: date }));
    } else {
      // Start new selection if clicked date is before start
      setSelectedDateRange({ start: date, end: null });
    }
  }, [availabilityData, selectedDateRange]);

  // Generate calendar data
  const calendarData = useMemo(() => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();

    // Get first day of month and last day of month
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    // Get first day of calendar (might be from previous month)
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    // Get last day of calendar (might be from next month)
    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));

    // Generate all days in the calendar
    const days = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      days.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return days;
  }, [currentMonth]);

  // Group bookings by date
  const bookingsByDate = useMemo(() => {
    const grouped = {};

    bookings.forEach(booking => {
      const checkIn = new Date(booking.dates.checkIn);
      const checkOut = new Date(booking.dates.checkOut);

      // Add booking to each date it spans
      const currentDate = new Date(checkIn);
      while (currentDate < checkOut) {
        const dateKey = currentDate.toISOString().split('T')[0];
        if (!grouped[dateKey]) {
          grouped[dateKey] = [];
        }
        grouped[dateKey].push({
          ...booking,
          isCheckIn: currentDate.toDateString() === checkIn.toDateString(),
          isCheckOut: currentDate.toDateString() === new Date(checkOut.getTime() - 24 * 60 * 60 * 1000).toDateString(),
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }
    });

    return grouped;
  }, [bookings]);

  // Get availability status for a date
  const getDateAvailability = useCallback((date) => {
    const dateStr = date.toISOString().split('T')[0];
    const availability = availabilityData[dateStr];
    const dayBookings = bookingsByDate[dateStr] || [];

    if (!availability) {
      return { status: 'loading', color: 'bg-gray-100', message: 'Loading...' };
    }

    if (!availability.isAvailable) {
      return {
        status: 'unavailable',
        color: 'bg-red-100 border-red-300',
        message: `Unavailable - ${availability.conflictingBookings?.length || 0} bookings`
      };
    }

    if (dayBookings.length === 0) {
      return { status: 'available', color: 'bg-green-100 border-green-300', message: 'Available' };
    }

    // Check if partially booked (some capacity remaining)
    const totalBookings = dayBookings.length;
    if (totalBookings > 0 && totalBookings < 3) { // Assuming max 3 bookings per day
      return {
        status: 'partial',
        color: 'bg-yellow-100 border-yellow-300',
        message: `${totalBookings} booking${totalBookings > 1 ? 's' : ''}`
      };
    }

    return {
      status: 'full',
      color: 'bg-orange-100 border-orange-300',
      message: `Fully booked (${totalBookings} bookings)`
    };
  }, [availabilityData, bookingsByDate]);

  // Check if date is in selected range
  const isDateInSelectedRange = useCallback((date) => {
    if (!selectedDateRange.start) return false;
    if (!selectedDateRange.end) return date.toDateString() === selectedDateRange.start.toDateString();

    return date >= selectedDateRange.start && date <= selectedDateRange.end;
  }, [selectedDateRange]);

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
    });
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'bg-yellow-100 border-yellow-300 text-yellow-800',
      confirmed: 'bg-blue-100 border-blue-300 text-blue-800',
      checked_in: 'bg-green-100 border-green-300 text-green-800',
      checked_out: 'bg-gray-100 border-gray-300 text-gray-800',
      cancelled: 'bg-red-100 border-red-300 text-red-800',
      no_show: 'bg-purple-100 border-purple-300 text-purple-800',
    };
    return colors[status] || 'bg-gray-100 border-gray-300 text-gray-800';
  };

  const navigateMonth = (direction) => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + direction);
    setCurrentMonth(newMonth);
  };

  const goToToday = () => {
    setCurrentMonth(new Date());
  };

  const isToday = (date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isCurrentMonth = (date) => {
    return date.getMonth() === currentMonth.getMonth();
  };

  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-7 gap-2">
            {[...Array(35)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      {/* Calendar Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-medium text-gray-900">
              {formatDate(currentMonth)}
            </h3>
            <div className="flex space-x-1">
              <button
                onClick={() => navigateMonth(-1)}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <button
                onClick={goToToday}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Today
              </button>
              <button
                onClick={() => navigateMonth(1)}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={onRefresh}
              className="p-2 text-gray-400 hover:text-gray-600"
              title="Refresh"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="p-6">
        {/* Day Headers */}
        <div className="grid grid-cols-7 gap-2 mb-2">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7 gap-2">
          {calendarData.map((date, index) => {
            const dateKey = date.toISOString().split('T')[0];
            const dayBookings = bookingsByDate[dateKey] || [];
            const availability = getDateAvailability(date);
            const isInSelectedRange = isDateInSelectedRange(date);

            return (
              <div
                key={index}
                onClick={() => handleDateClick(date)}
                onMouseEnter={() => setHoveredDate(date)}
                onMouseLeave={() => setHoveredDate(null)}
                className={`min-h-24 p-2 border rounded-lg cursor-pointer transition-all duration-200 ${
                  isCurrentMonth(date)
                    ? `bg-white ${availability.color}`
                    : 'bg-gray-50 border-gray-100'
                } ${
                  isToday(date)
                    ? 'ring-2 ring-blue-500'
                    : ''
                } ${
                  isInSelectedRange
                    ? 'ring-2 ring-purple-500 bg-purple-50'
                    : ''
                } ${
                  hoveredDate && hoveredDate.toDateString() === date.toDateString()
                    ? 'shadow-md scale-105'
                    : ''
                } ${
                  availability.status === 'unavailable'
                    ? 'cursor-not-allowed opacity-60'
                    : 'hover:shadow-md'
                }`}
                title={availability.message}
              >
                {/* Date Number with Availability Indicator */}
                <div className="flex items-center justify-between mb-1">
                  <div className={`text-sm font-medium ${
                    isCurrentMonth(date)
                      ? isToday(date)
                        ? 'text-blue-600'
                        : 'text-gray-900'
                      : 'text-gray-400'
                  }`}>
                    {date.getDate()}
                  </div>

                  {/* Availability Status Indicator */}
                  <div className="flex items-center space-x-1">
                    {availability.status === 'available' && (
                      <div className="w-2 h-2 bg-green-500 rounded-full" title="Available"></div>
                    )}
                    {availability.status === 'partial' && (
                      <div className="w-2 h-2 bg-yellow-500 rounded-full" title="Partially Booked"></div>
                    )}
                    {availability.status === 'unavailable' && (
                      <div className="w-2 h-2 bg-red-500 rounded-full" title="Unavailable"></div>
                    )}
                    {availability.status === 'full' && (
                      <div className="w-2 h-2 bg-orange-500 rounded-full" title="Fully Booked"></div>
                    )}
                    {isLoadingAvailability && (
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" title="Loading..."></div>
                    )}
                  </div>
                </div>

                {/* Bookings */}
                <div className="space-y-1">
                  {dayBookings.slice(0, 2).map((booking, bookingIndex) => (
                    <button
                      key={`${booking._id}-${bookingIndex}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        onBookingSelect(booking);
                      }}
                      className={`w-full text-left px-2 py-1 rounded text-xs border ${getStatusColor(booking.status)} hover:opacity-80 transition-opacity`}
                      title={`${booking.customer?.firstname || booking.customer?.name} ${booking.customer?.surname || ''} - ${booking.package?.name} (${booking.status})`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="truncate">
                          {booking.isCheckIn && '→ '}
                          {booking.isCheckOut && '← '}
                          {booking.customer?.firstname || booking.customer?.name}
                        </span>
                        {booking.isCheckIn && (
                          <span className="text-xs">IN</span>
                        )}
                        {booking.isCheckOut && (
                          <span className="text-xs">OUT</span>
                        )}
                      </div>
                    </button>
                  ))}

                  {dayBookings.length > 2 && (
                    <div className="text-xs text-gray-500 text-center">
                      +{dayBookings.length - 2} more
                    </div>
                  )}

                  {/* Show availability info if no bookings */}
                  {dayBookings.length === 0 && isCurrentMonth(date) && (
                    <div className="text-xs text-gray-400 text-center">
                      {availability.message}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Legend */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="space-y-3">
          {/* Booking Status Legend */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-gray-700">Booking Status:</span>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-yellow-100 border border-yellow-300 rounded"></div>
                <span className="text-xs text-gray-600">Pending</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-blue-100 border border-blue-300 rounded"></div>
                <span className="text-xs text-gray-600">Confirmed</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                <span className="text-xs text-gray-600">Checked In</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-gray-100 border border-gray-300 rounded"></div>
                <span className="text-xs text-gray-600">Checked Out</span>
              </div>
            </div>

            <div className="text-sm text-gray-500">
              → Check-in • ← Check-out
            </div>
          </div>

          {/* Availability Legend */}
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Availability:</span>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-600">Available</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span className="text-xs text-gray-600">Partially Booked</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span className="text-xs text-gray-600">Fully Booked</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span className="text-xs text-gray-600">Unavailable</span>
            </div>
          </div>
        </div>
      </div>

      {/* Package Filter and Selected Date Range */}
      <div className="px-6 py-4 border-t border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">Filter by Package:</label>
            <select
              value={selectedPackage}
              onChange={(e) => setSelectedPackage(e.target.value)}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Packages</option>
              {packages.map(pkg => (
                <option key={pkg._id} value={pkg._id}>
                  {pkg.name} ({pkg.category})
                </option>
              ))}
            </select>
          </div>

          {selectedDateRange.start && (
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Selected: {selectedDateRange.start.toLocaleDateString()}
                {selectedDateRange.end && ` - ${selectedDateRange.end.toLocaleDateString()}`}
              </span>
              <button
                onClick={() => setSelectedDateRange({ start: null, end: null })}
                className="text-sm text-red-600 hover:text-red-800"
              >
                Clear Selection
              </button>
              <button
                onClick={() => setShowBookingModal(true)}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                Create Booking
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
