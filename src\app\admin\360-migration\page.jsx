'use client';

import { useState, useEffect } from 'react';

export default function ThreeSixtyMigrationPage() {
  const [migrationStatus, setMigrationStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [migrationResults, setMigrationResults] = useState(null);

  // Fetch migration status on component mount
  useEffect(() => {
    fetchMigrationStatus();
  }, []);

  const fetchMigrationStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/360s/migrate-to-firebase');
      const data = await response.json();
      
      if (data.success) {
        setMigrationStatus(data);
      } else {
        console.error('Failed to fetch migration status:', data.error);
      }
    } catch (error) {
      console.error('Error fetching migration status:', error);
    } finally {
      setLoading(false);
    }
  };

  const runMigration = async (dryRun = true, cleanup = false) => {
    try {
      setLoading(true);
      setMigrationResults(null);

      const response = await fetch('/api/360s/migrate-to-firebase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ dryRun, cleanup }),
      });

      const data = await response.json();
      
      if (data.success) {
        setMigrationResults(data);
        // Refresh status after migration
        await fetchMigrationStatus();
      } else {
        console.error('Migration failed:', data.error);
        setMigrationResults({
          success: false,
          error: data.error,
        });
      }
    } catch (error) {
      console.error('Error running migration:', error);
      setMigrationResults({
        success: false,
        error: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">
              360° URL Migration to Firebase
            </h1>
            <p className="mt-2 text-gray-600">
              Migrate 360° images from local storage to Firebase Storage
            </p>
          </div>

          <div className="p-6">
            {/* Migration Status */}
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Current Status
              </h2>
              
              {loading && !migrationStatus ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading status...</p>
                </div>
              ) : migrationStatus ? (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {migrationStatus.status.total}
                    </div>
                    <div className="text-sm text-blue-800">Total Records</div>
                  </div>
                  
                  <div className="bg-red-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">
                      {migrationStatus.status.needsMigration}
                    </div>
                    <div className="text-sm text-red-800">Need Migration</div>
                  </div>
                  
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {migrationStatus.status.alreadyMigrated}
                    </div>
                    <div className="text-sm text-green-800">Already Migrated</div>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-gray-600">
                      {migrationStatus.status.other}
                    </div>
                    <div className="text-sm text-gray-800">Other URLs</div>
                  </div>
                </div>
              ) : (
                <div className="text-red-600">Failed to load status</div>
              )}

              {migrationStatus?.recommendations && (
                <div className={`mt-4 p-4 rounded-lg ${
                  migrationStatus.recommendations.shouldMigrate 
                    ? 'bg-yellow-50 border border-yellow-200' 
                    : 'bg-green-50 border border-green-200'
                }`}>
                  <p className={`font-medium ${
                    migrationStatus.recommendations.shouldMigrate 
                      ? 'text-yellow-800' 
                      : 'text-green-800'
                  }`}>
                    {migrationStatus.recommendations.message}
                  </p>
                </div>
              )}
            </div>

            {/* Sample Records */}
            {migrationStatus?.samples && (
              <div className="mb-8">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Sample Records
                </h2>
                
                {migrationStatus.samples.local.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-md font-medium text-red-700 mb-2">
                      Records Needing Migration ({migrationStatus.samples.local.length} shown)
                    </h3>
                    <div className="bg-red-50 rounded-lg p-4">
                      {migrationStatus.samples.local.map((record) => (
                        <div key={record._id} className="mb-2 last:mb-0">
                          <div className="font-medium text-red-900">{record.name}</div>
                          <div className="text-sm text-red-700 font-mono">{record.url}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {migrationStatus.samples.firebase.length > 0 && (
                  <div>
                    <h3 className="text-md font-medium text-green-700 mb-2">
                      Already Migrated Records ({migrationStatus.samples.firebase.length} shown)
                    </h3>
                    <div className="bg-green-50 rounded-lg p-4">
                      {migrationStatus.samples.firebase.map((record) => (
                        <div key={record._id} className="mb-2 last:mb-0">
                          <div className="font-medium text-green-900">{record.name}</div>
                          <div className="text-sm text-green-700 font-mono truncate">{record.url}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Migration Actions */}
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Migration Actions
              </h2>
              
              <div className="space-y-4">
                <div className="flex flex-wrap gap-4">
                  <button
                    onClick={() => runMigration(true, false)}
                    disabled={loading}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? 'Running...' : 'Dry Run (Preview)'}
                  </button>
                  
                  <button
                    onClick={() => runMigration(false, false)}
                    disabled={loading || migrationStatus?.status?.needsMigration === 0}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? 'Running...' : 'Run Migration'}
                  </button>
                  
                  <button
                    onClick={() => runMigration(false, true)}
                    disabled={loading || migrationStatus?.status?.needsMigration === 0}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? 'Running...' : 'Migrate + Cleanup Local Files'}
                  </button>
                  
                  <button
                    onClick={fetchMigrationStatus}
                    disabled={loading}
                    className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Refresh Status
                  </button>
                </div>
                
                <div className="text-sm text-gray-600">
                  <p><strong>Dry Run:</strong> Preview what would be migrated without making changes</p>
                  <p><strong>Run Migration:</strong> Actually migrate files to Firebase and update database</p>
                  <p><strong>Migrate + Cleanup:</strong> Migrate files and delete local copies after successful upload</p>
                </div>
              </div>
            </div>

            {/* Migration Results */}
            {migrationResults && (
              <div className="mb-8">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Migration Results
                </h2>
                
                {migrationResults.success ? (
                  <div className="space-y-4">
                    <div className={`p-4 rounded-lg ${
                      migrationResults.dryRun ? 'bg-blue-50' : 'bg-green-50'
                    }`}>
                      <p className={`font-medium ${
                        migrationResults.dryRun ? 'text-blue-800' : 'text-green-800'
                      }`}>
                        {migrationResults.message}
                      </p>
                    </div>
                    
                    {migrationResults.report && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="bg-gray-50 p-3 rounded">
                          <div className="text-lg font-bold">{migrationResults.report.total}</div>
                          <div className="text-sm text-gray-600">Total</div>
                        </div>
                        <div className="bg-green-50 p-3 rounded">
                          <div className="text-lg font-bold text-green-600">{migrationResults.report.migrated}</div>
                          <div className="text-sm text-green-800">Migrated</div>
                        </div>
                        <div className="bg-blue-50 p-3 rounded">
                          <div className="text-lg font-bold text-blue-600">{migrationResults.report.alreadyMigrated}</div>
                          <div className="text-sm text-blue-800">Already Done</div>
                        </div>
                        <div className="bg-red-50 p-3 rounded">
                          <div className="text-lg font-bold text-red-600">{migrationResults.report.failed}</div>
                          <div className="text-sm text-red-800">Failed</div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="bg-red-50 p-4 rounded-lg">
                    <p className="font-medium text-red-800">Migration Failed</p>
                    <p className="text-red-700">{migrationResults.error}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
