import mongoose from 'mongoose';
const { Schema } = mongoose;

const HeroImageSchema = new Schema({
    name: { type: String, required: true },
    url: { type: String, required: true },
    fullPath: { type: String, default: '' },
    contentType: { type: String, default: 'image/jpeg' },
    size: { type: Number, default: 0 },
    uploadedAt: { type: String, default: '' }
}, { timestamps: true });

export const HeroImage = mongoose.models.HeroImage || mongoose.model('HeroImage', HeroImageSchema);
