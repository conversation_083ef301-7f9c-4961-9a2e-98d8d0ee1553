'use client'

import Image from 'next/image'
import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import { settings } from '@/lib/settings'
import { useContextExperience } from '@/contexts/useContextExperience'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'

function BtnLandingpageComponent({data,fn,index}) {
    const [swap,setSwap]=useState(true)
    // console.log('BtnLandingpageComponent:',index)
    return(
        <div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                onClick={fn?.[index]} 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.btnIcons?.ov}/>
            </div>
            <div
                onClick={fn?.[index]} 
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <ImageWrapperResponsive className={'w-auto h-full'} alt='button images for landpage options' src={data?.btnIcons?.off}/>
            </div>
        </div>
    )
}

export default function LandingpageComponentCarousel() {
    const {experienceState,disptachExperience}=useContextExperience()
    const router=useRouter()

    const handleOnExplore = () => {
        router.push('/hero-video')
        // console.log('explore')
    }
    const handleOnBookNow = () => {
        // router.push('/booking')
        // console.log('LandingpageComponent:',experienceState?.showPopup)
        disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_BOOKING_TOGGLE})
    }

    // console.log('LandingpageComponent:',experienceState?.showPopup)

  return (
    <div className='flex items-center justify-center w-fit h-fit'>
        {settings.landingPage.btns.map((i,index)=>
            // console.log(i)
            <BtnLandingpageComponent 
                fn={[handleOnExplore,handleOnBookNow]} 
                index={index}
                key={index} 
                data={i}
            />
        )}
    </div>
  )
}
