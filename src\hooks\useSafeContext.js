'use client'

import { useContext } from 'react'
import { ExperienceContext } from '@/contexts/useContextExperience'
import { MarkerContext } from '@/contexts/MarkerContext'

/**
 * Safe context hooks that provide fallbacks when providers are not available
 * These hooks prevent the "must be used within provider" errors
 */

/**
 * Safe version of useContextExperience
 * Returns null when provider is not available instead of throwing
 */
export function useSafeExperienceContext() {
  try {
    const context = useContext(ExperienceContext)
    
    // Return null if context is not available (no provider)
    if (!context) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('useSafeExperienceContext: ExperienceContextProvider not found in component tree')
      }
      return null
    }
    
    return context
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('useSafeExperienceContext error:', error)
    }
    return null
  }
}

/**
 * Safe version of useMarkerContext
 * Returns null when provider is not available instead of throwing
 */
export function useSafeMarkerContext() {
  try {
    const context = useContext(MarkerContext)
    
    // Return null if context is not available (no provider)
    if (!context) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('useSafeMarkerContext: MarkerContextProvider not found in component tree')
      }
      return null
    }
    
    return context
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('useSafeMarkerContext error:', error)
    }
    return null
  }
}

/**
 * Hook that provides fallback values when ExperienceContext is not available
 */
export function useExperienceContextWithFallback() {
  const context = useSafeExperienceContext()
  
  // Provide safe fallback values
  if (!context) {
    return {
      experienceState: {
        showNavbar: false,
        showMenu: false,
        showPopup: false,
        showBookingPopup: false,
        showGalleryStore: false,
        showVideoGallery: false,
        showItemInfo: false,
        id: '',
      },
      disptachExperience: (action) => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('ExperienceContext not available, action ignored:', action)
        }
      }
    }
  }
  
  return context
}

/**
 * Hook that provides fallback values when MarkerContext is not available
 */
export function useMarkerContextWithFallback() {
  const context = useSafeMarkerContext()
  
  // Provide safe fallback values
  if (!context) {
    return {
      markerState: {
        selectedMarker: null,
        activeMarkerId: null,
        markerData: {},
        isMarkerPopupOpen: false,
      },
      selectedMarker: null,
      activeMarkerId: null,
      isMarkerPopupOpen: false,
      setSelectedMarker: (marker) => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('MarkerContext not available, setSelectedMarker ignored:', marker)
        }
      },
      setMarkerData: (id, data) => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('MarkerContext not available, setMarkerData ignored:', id, data)
        }
      },
      getMarkerData: (id) => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('MarkerContext not available, getMarkerData returning null for:', id)
        }
        return null
      },
      clearMarkerData: (id) => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('MarkerContext not available, clearMarkerData ignored:', id)
        }
      },
      toggleMarkerPopup: () => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('MarkerContext not available, toggleMarkerPopup ignored')
        }
      },
      handleMarkerClick: (marker) => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('MarkerContext not available, handleMarkerClick ignored:', marker)
        }
      },
      resetMarkerState: () => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('MarkerContext not available, resetMarkerState ignored')
        }
      },
    }
  }
  
  return context
}

/**
 * Combined hook that checks if both contexts are available
 */
export function useCombinedContexts() {
  const experienceContext = useSafeExperienceContext()
  const markerContext = useSafeMarkerContext()
  
  return {
    experienceContext: experienceContext || useExperienceContextWithFallback(),
    markerContext: markerContext || useMarkerContextWithFallback(),
    hasExperienceContext: !!experienceContext,
    hasMarkerContext: !!markerContext,
    hasAllContexts: !!(experienceContext && markerContext)
  }
}
