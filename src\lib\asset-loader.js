'use client';

import React from 'react';

/**
 * Enhanced Asset Loading Utility
 * Provides caching, error handling, and fallback mechanisms for asset loading
 * Specifically designed to prevent 429 errors and improve performance
 */

// Asset cache to prevent repeated requests
const assetCache = new Map();
const loadingPromises = new Map();

// Configuration
const ASSET_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  cacheTimeout: 5 * 60 * 1000, // 5 minutes
  requestTimeout: 10000, // 10 seconds
};

/**
 * Enhanced image preloader with caching, error handling, and production URL resolution
 * @param {string} src - Image source URL
 * @param {Object} options - Loading options
 * @returns {Promise<HTMLImageElement>}
 */
export async function loadImageWithCache(src, options = {}) {
  const {
    maxRetries = ASSET_CONFIG.maxRetries,
    retryDelay = ASSET_CONFIG.retryDelay,
    timeout = ASSET_CONFIG.requestTimeout,
    id = 'unknown',
  } = options;

  // Validate and resolve URL for production
  const resolvedSrc = resolveAssetUrl(src);
  if (!resolvedSrc) {
    throw new Error(`Invalid asset URL: ${src}`);
  }

  // Check cache first
  const cacheKey = resolvedSrc;
  const cached = assetCache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < ASSET_CONFIG.cacheTimeout) {
    return cached.image;
  }

  // Check if already loading
  if (loadingPromises.has(cacheKey)) {
    return loadingPromises.get(cacheKey);
  }

  // Create loading promise with fallback support
  const loadingPromise = loadImageWithFallback(resolvedSrc, maxRetries, retryDelay, timeout, id);
  loadingPromises.set(cacheKey, loadingPromise);

  try {
    const image = await loadingPromise;

    // Cache successful result
    assetCache.set(cacheKey, {
      image,
      timestamp: Date.now(),
    });

    return image;
  } catch (error) {
    console.warn(`Failed to load image after ${maxRetries} retries:`, resolvedSrc, error);
    throw error;
  } finally {
    loadingPromises.delete(cacheKey);
  }
}

/**
 * Resolve asset URL for production compatibility
 * @param {string} src - Original asset URL
 * @returns {string|null} - Resolved URL or null if invalid
 */
function resolveAssetUrl(src) {
  if (!src || typeof src !== 'string') {
    return null;
  }

  // If it's already a full URL (Firebase, CDN, etc.), return as-is
  if (src.startsWith('http://') || src.startsWith('https://')) {
    return src;
  }

  // For local assets in production, use our serving API
  if (process.env.NODE_ENV === 'production') {
    // Extract filename and determine asset type
    const filename = src.split('/').pop();

    // Route through appropriate serving API based on path
    if (src.includes('/360s/') || src.includes('360')) {
      return `/api/360s/serve/${filename}`;
    } else {
      return `/api/assets/serve/${filename}`;
    }
  }

  // In development, ensure proper path format
  return src.startsWith('/') ? src : `/${src}`;
}

/**
 * Load image with retry logic and fallback support
 * @param {string} src - Image source URL
 * @param {number} maxRetries - Maximum retry attempts
 * @param {number} retryDelay - Delay between retries
 * @param {number} timeout - Request timeout
 * @param {string} id - Asset ID for fallback generation
 * @returns {Promise<HTMLImageElement>}
 */
async function loadImageWithFallback(src, maxRetries, retryDelay, timeout, id) {
  let lastError;

  // Try primary URL first
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await loadImageWithTimeout(src, timeout);
    } catch (error) {
      lastError = error;
      console.warn(`Asset loading attempt ${attempt + 1} failed for ${src}:`, error);

      if (attempt < maxRetries) {
        // Add exponential backoff to prevent overwhelming the server
        const delay = retryDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // If primary URL fails completely, try fallback URLs
  const fallbackUrls = generateAssetFallbackUrls(src, id);

  for (const fallbackUrl of fallbackUrls) {
    try {
      console.log(`Trying fallback URL: ${fallbackUrl}`);
      return await loadImageWithTimeout(fallbackUrl, timeout);
    } catch (fallbackError) {
      console.warn(`Fallback URL failed: ${fallbackUrl}`, fallbackError);
      continue;
    }
  }

  throw lastError;
}

/**
 * Generate fallback URLs for assets
 * @param {string} originalUrl - Original URL that failed
 * @param {string} id - Asset ID
 * @returns {string[]} - Array of fallback URLs
 */
function generateAssetFallbackUrls(originalUrl, id) {
  const fallbacks = [];

  // Extract filename from original URL
  const filename = originalUrl.split('/').pop();
  const nameWithoutExt = filename.split('.')[0];
  const ext = filename.split('.').pop();

  // Try different common asset paths
  fallbacks.push(`/assets/${filename}`);
  fallbacks.push(`/assets/images/${filename}`);
  fallbacks.push(`/uploads/${filename}`);
  fallbacks.push(`/public/assets/${filename}`);

  // Try with different extensions
  const commonExts = ['png', 'jpg', 'jpeg', 'webp', 'svg'];
  for (const commonExt of commonExts) {
    if (commonExt !== ext) {
      fallbacks.push(`/assets/${nameWithoutExt}.${commonExt}`);
      fallbacks.push(`/assets/images/${nameWithoutExt}.${commonExt}`);
    }
  }

  // Default fallback images
  fallbacks.push('/assets/placeholder.png');
  fallbacks.push('/assets/images/placeholder.png');
  fallbacks.push('/assets/default.png');

  return fallbacks;
}

/**
 * Legacy function for backward compatibility
 * @param {string} src - Image source URL
 * @param {number} maxRetries - Maximum retry attempts
 * @param {number} retryDelay - Delay between retries
 * @param {number} timeout - Request timeout
 * @returns {Promise<HTMLImageElement>}
 */
async function loadImageWithRetry(src, maxRetries, retryDelay, timeout) {
  return loadImageWithFallback(src, maxRetries, retryDelay, timeout, 'legacy');
}

/**
 * Load image with timeout
 * @param {string} src - Image source URL
 * @param {number} timeout - Request timeout
 * @returns {Promise<HTMLImageElement>}
 */
function loadImageWithTimeout(src, timeout) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    let timeoutId;

    const cleanup = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      img.onload = null;
      img.onerror = null;
    };

    // Set timeout
    timeoutId = setTimeout(() => {
      cleanup();
      reject(new Error(`Image loading timeout: ${src}`));
    }, timeout);

    img.onload = () => {
      cleanup();
      resolve(img);
    };

    img.onerror = (error) => {
      cleanup();
      reject(new Error(`Image loading failed: ${src} - ${error.message || 'Unknown error'}`));
    };

    // Set crossOrigin for Three.js compatibility
    // Always set crossOrigin for local development to prevent CORS issues
    img.crossOrigin = 'anonymous';

    // Start loading
    img.src = src;
  });
}

/**
 * Preload multiple images with priority-based loading
 * @param {Array} imageSources - Array of image sources with optional priority
 * @param {Object} options - Loading options
 * @returns {Promise<Map>} - Map of src to loaded images
 */
export async function preloadImages(imageSources, options = {}) {
  const {
    concurrency = 3, // Limit concurrent requests to prevent 429 errors
    priorityFirst = true,
  } = options;

  // Sort by priority if specified
  const sortedSources = priorityFirst 
    ? [...imageSources].sort((a, b) => (a.priority || 0) - (b.priority || 0))
    : imageSources;

  const results = new Map();
  const errors = [];

  // Process in batches to control concurrency
  for (let i = 0; i < sortedSources.length; i += concurrency) {
    const batch = sortedSources.slice(i, i + concurrency);
    
    const batchPromises = batch.map(async (source) => {
      const src = typeof source === 'string' ? source : source.src;
      
      try {
        const image = await loadImageWithCache(src, options);
        results.set(src, image);
        return { src, success: true, image };
      } catch (error) {
        errors.push({ src, error });
        return { src, success: false, error };
      }
    });

    // Wait for current batch to complete before starting next
    await Promise.allSettled(batchPromises);
    
    // Add small delay between batches to prevent overwhelming the server
    if (i + concurrency < sortedSources.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  if (errors.length > 0) {
    console.warn(`Failed to load ${errors.length} images:`, errors);
  }

  return results;
}

/**
 * Clear asset cache
 * @param {string} src - Optional specific source to clear, or clear all if not provided
 */
export function clearAssetCache(src = null) {
  if (src) {
    assetCache.delete(src);
    loadingPromises.delete(src);
  } else {
    assetCache.clear();
    loadingPromises.clear();
  }
}

/**
 * Get cache statistics
 * @returns {Object} Cache statistics
 */
export function getCacheStats() {
  return {
    cacheSize: assetCache.size,
    loadingCount: loadingPromises.size,
    cacheEntries: Array.from(assetCache.keys()),
  };
}

/**
 * React hook for loading images with caching
 * @param {string} src - Image source URL
 * @param {Object} options - Loading options
 * @returns {Object} Loading state and image
 */
export function useImageLoader(src, options = {}) {
  const [state, setState] = React.useState({
    loading: true,
    error: null,
    image: null,
  });

  React.useEffect(() => {
    if (!src) {
      setState({ loading: false, error: null, image: null });
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    loadImageWithCache(src, options)
      .then(image => {
        setState({ loading: false, error: null, image });
      })
      .catch(error => {
        setState({ loading: false, error, image: null });
      });
  }, [src]);

  return state;
}
