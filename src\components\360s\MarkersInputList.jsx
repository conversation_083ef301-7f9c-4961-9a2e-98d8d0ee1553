'use client';

import { settings } from '@/lib/settings';
import React, { useEffect, useState, useCallback, useMemo, memo, useRef } from 'react'
import { BiTrash } from 'react-icons/bi'
import useNetworkStatus from '@/hooks/useNetworkStatus'
import OfflineNotification from './OfflineNotification'

const css='flex items-center w-full h-8 bg-gray-50 rounded shadow placeholder:text-xs text-xs placeholder:text-gray-600 text-gray-600 px-2 outline-none'

function MarkersInputList({_360sList,_360Object, set_360Object}) {
  const [markerList, setMarkerList] = useState([])
  const [input, setInput] = useState({ name: '', markerType: '' })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState({ type: '', message: '' })
  const debounceTimeoutRef = useRef(null)

  // Network status monitoring
  const isOnline = useNetworkStatus()

  // API data states for dynamic selects
  const [apiData, setApiData] = useState({
    videoGallery: [],
    infoMarkers: [],
    stores: []
  })
  const [apiLoading, setApiLoading] = useState({
    videoGallery: false,
    infoMarkers: false,
    stores: false
  })
  const [apiErrors, setApiErrors] = useState({
    videoGallery: null,
    infoMarkers: null,
    stores: null
  })

  // API fetching functions
  const fetchApiData = useCallback(async (endpoint, dataKey) => {
    if (apiData[dataKey].length > 0) return // Don't refetch if data exists

    setApiLoading(prev => ({ ...prev, [dataKey]: true }))
    setApiErrors(prev => ({ ...prev, [dataKey]: null }))

    try {
      const response = await fetch(endpoint)
      const result = await response.json()

      if (result.success && result.data) {
        setApiData(prev => ({ ...prev, [dataKey]: result.data }))
      } else {
        setApiErrors(prev => ({ ...prev, [dataKey]: `Failed to load ${dataKey}` }))
      }
    } catch (error) {
      console.error(`Error fetching ${dataKey}:`, error)
      setApiErrors(prev => ({ ...prev, [dataKey]: `Network error loading ${dataKey}` }))
    } finally {
      setApiLoading(prev => ({ ...prev, [dataKey]: false }))
    }
  }, [apiData])

  // Fetch data when component mounts
  useEffect(() => {
    fetchApiData('/api/video-gallery', 'videoGallery')
    fetchApiData('/api/info-markers', 'infoMarkers')
    fetchApiData('/api/stores', 'stores')
  }, [fetchApiData])

  // Enhanced handleAddList function with validation and feedback
  const handleAddList = useCallback(() => {
    // Validate input
    if (!input?.name || !input?.markerType) {
      setSubmitStatus({
        type: 'error',
        message: 'Please enter both marker name and type before adding.'
      })
      return
    }

    // Check for duplicate marker names
    const isDuplicate = markerList.some(marker =>
      marker.name.toLowerCase() === input.name.toLowerCase()
    )

    if (isDuplicate) {
      setSubmitStatus({
        type: 'error',
        message: 'A marker with this name already exists. Please use a different name.'
      })
      return
    }

    // Create new marker with default values
    const newMarker = {
      name: input.name.trim(),
      markerType: input.markerType,
      x: 0,
      y: 0,
      z: 0,
      // Add additional fields based on marker type
      ...(input.markerType === 'landingPage' ||
          input.markerType === 'guide' ||
          input.markerType === 'upstairs' ||
          input.markerType === 'downstairs'
          ? { _360Name: '' } // Navigation markers use _360Name
          : { id: '' }) // Content markers (infoVideo, infoDoc, infoImage) use id
    }

    // Add marker to list and immediately update parent state
    const updatedMarkerList = [...markerList, newMarker]
    setMarkerList(updatedMarkerList)

    // Immediately update parent state for real-time UI sync
    if (typeof set_360Object === 'function') {
      set_360Object(prev => ({
        ...prev,
        markerList: updatedMarkerList
      }))
    }

    // Clear input and show success message
    setInput({ name: '', markerType: '' })
    setSubmitStatus({
      type: 'success',
      message: `Marker "${newMarker.name}" added successfully! Don't forget to submit to save changes.`
    })

    // console.log('New marker added:', newMarker)
  }, [input, markerList, set_360Object])

  // Memoize the marker list from _360Object to prevent unnecessary updates
  const currentMarkerList = useMemo(() => {
    return _360Object?.markerList || []
  }, [_360Object?.markerList])

  // Memoize the current _360Object ID to prevent unnecessary re-renders
  const current360Id = useMemo(() => _360Object?._id, [_360Object?._id])

  // Migrate old marker data structure and initialize marker list
  const migrateMarkerData = useCallback((markers) => {
    return markers.map(marker => {
      // Migrate old infoType field to id field for content markers
      if (marker.infoType && !marker.id &&
          (marker.markerType === 'infoVideo' ||
           marker.markerType === 'infoDoc' ||
           marker.markerType === 'infoImage')) {
        const { infoType, ...rest } = marker;
        return { ...rest, id: '' }; // Reset to empty string for proper selection
      }
      return marker;
    });
  }, []);

  // Initialize marker list from _360Object with proper synchronization
  useEffect(() => {
    // Always update local marker list when _360Object changes to ensure synchronization
    if (current360Id !== undefined) { // Check for defined ID (including empty string)
      const migratedMarkers = migrateMarkerData(currentMarkerList);
      setMarkerList(migratedMarkers);

      // Clear any pending status messages when switching images
      setSubmitStatus({ type: '', message: '' });
    }
  }, [current360Id, currentMarkerList, migrateMarkerData]) // Use memoized ID and marker list

  // FIXED: Sync FROM parent TO local state to capture Leva control updates
  // This ensures position updates from Leva controls are reflected in local state
  useEffect(() => {
    if (!_360Object || !_360Object._id) return

    // Don't sync if we're in the middle of loading a new image
    if (_360Object._id !== current360Id) return

    const parentMarkerList = _360Object.markerList || []

    // Check if parent has position updates that local state doesn't have
    const hasParentUpdates = parentMarkerList.some((parentMarker, index) => {
      const localMarker = markerList[index]
      if (!localMarker || !parentMarker) return false

      // Check for position changes from Leva controls
      return parentMarker.name === localMarker.name &&
             (parentMarker.x !== localMarker.x ||
              parentMarker.y !== localMarker.y ||
              parentMarker.z !== localMarker.z)
    })

    // Only update if there are meaningful position changes from parent
    if (hasParentUpdates && parentMarkerList.length === markerList.length) {
      // Clear any existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }

      // Debounce the update to prevent excessive re-renders
      debounceTimeoutRef.current = setTimeout(() => {
        setMarkerList([...parentMarkerList]) // Sync local state with parent
      }, 50) // Short debounce for responsiveness
    }

    // Cleanup timeout on unmount
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [_360Object.markerList, markerList, current360Id])

  // Clear status messages after 5 seconds
  useEffect(() => {
    if (submitStatus.message) {
      const timer = setTimeout(() => {
        setSubmitStatus({ type: '', message: '' })
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [submitStatus.message])

  // Enhanced handleSubmit function with comprehensive error handling and user feedback
  const handleSubmit = useCallback(async () => {
    if (!_360Object?._id) {
      setSubmitStatus({
        type: 'error',
        message: 'No 360° image selected. Please select an image first.'
      })
      return
    }

    setIsSubmitting(true)
    setSubmitStatus({ type: 'loading', message: 'Saving changes...' })

    try {
      // CRITICAL FIX: Use _360Object.markerList instead of local markerList
      // This ensures we capture all Leva control position updates
      const finalMarkerList = _360Object.markerList || markerList || []

      // Prepare the payload with camera settings, marker data, and name if changed
      const payload = {
        cameraPosition: _360Object.cameraPosition,
        _360Rotation: _360Object._360Rotation,
        markerList: finalMarkerList // Use the most current marker data from parent state
      }

      // Include name field if it exists in _360Object (for name updates)
      if (_360Object.name) {
        payload.name = _360Object.name
      }

      console.log('Submitting payload with final marker list:', {
        localMarkerCount: markerList.length,
        parentMarkerCount: _360Object.markerList?.length || 0,
        finalMarkerCount: finalMarkerList.length,
        payload
      })

      const response = await fetch(`/api/360s/${_360Object._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      const result = await response.json()

      if (!response.ok) {
        // Handle HTTP error responses
        if (response.status === 400) {
          setSubmitStatus({
            type: 'error',
            message: `Validation Error: ${result.message || 'Invalid data provided'}`
          })
        } else if (response.status === 401) {
          setSubmitStatus({
            type: 'error',
            message: 'Authentication required. Please sign in and try again.'
          })
        } else if (response.status === 403) {
          setSubmitStatus({
            type: 'error',
            message: 'Permission denied. You need manager/admin access to save changes.'
          })
        } else if (response.status === 404) {
          setSubmitStatus({
            type: 'error',
            message: '360° image not found. It may have been deleted.'
          })
        } else if (response.status >= 500) {
          setSubmitStatus({
            type: 'error',
            message: 'Server error. Please try again later.'
          })
        } else {
          setSubmitStatus({
            type: 'error',
            message: `Error: ${result.message || 'Unknown error occurred'}`
          })
        }
        return
      }

      if (result.success) {
        console.log('360° data updated successfully:', {
          submittedMarkers: finalMarkerList.length,
          returnedMarkers: result.data.markerList?.length || 0,
          resultData: result.data
        })

        // Update the parent component with the fresh database data
        if (typeof set_360Object === 'function') {
          set_360Object(result.data)
        }

        // Update local state to match the database response
        setMarkerList(result.data.markerList || [])

        // Trigger a refresh of the parent component's data to ensure full synchronization
        if (typeof window !== 'undefined' && window.refreshDashboardData) {
          // Use setTimeout to ensure state updates complete before refresh
          setTimeout(() => {
            window.refreshDashboardData()
          }, 100)
        }

        setSubmitStatus({
          type: 'success',
          message: 'Changes saved successfully! Camera settings and markers updated.'
        })
      } else {
        setSubmitStatus({
          type: 'error',
          message: `Save failed: ${result.message || 'Unknown error'}`
        })
      }
    } catch (error) {
      console.error('Error submitting 360° data:', error)

      // Handle different types of errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        setSubmitStatus({
          type: 'error',
          message: 'Network error. Please check your connection and try again.'
        })
      } else if (error.name === 'SyntaxError') {
        setSubmitStatus({
          type: 'error',
          message: 'Server response error. Please try again.'
        })
      } else {
        setSubmitStatus({
          type: 'error',
          message: `Unexpected error: ${error.message}`
        })
      }
    } finally {
      setIsSubmitting(false)
    }
  }, [_360Object, markerList, set_360Object])

  // Memoize the delete marker function with immediate parent state update
  const handleDeleteMarker = useCallback((markerName) => {
    const updatedMarkerList = markerList.filter(item => item?.name !== markerName)
    setMarkerList(updatedMarkerList)

    // Immediately update parent state for real-time UI sync
    if (typeof set_360Object === 'function') {
      set_360Object(prev => ({
        ...prev,
        markerList: updatedMarkerList
      }))
    }
  }, [markerList, set_360Object])

  // Memoize the update marker function with improved debouncing and parent state sync
  const handleUpdateMarker = useCallback((markerName, updates) => {
    // Immediately update the UI for better responsiveness
    const updatedMarkerList = markerList.map(item =>
      item?.name === markerName ? { ...item, ...updates } : item
    )
    setMarkerList(updatedMarkerList)

    // Immediately update parent state for real-time UI sync
    if (typeof set_360Object === 'function') {
      set_360Object(prev => ({
        ...prev,
        markerList: updatedMarkerList
      }))
    }
  }, [markerList, set_360Object])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [])

  // Memoize marker type options to prevent unnecessary re-renders
  const markerTypeOptions = useMemo(() =>
    settings.markerList.markerType.map((type, index) => (
      <option key={index} value={type}>{type}</option>
    )), []
  )

  // Memoize dynamic select options for content markers
  const getSelectOptions = useCallback((markerType) => {
    switch (markerType) {
      case 'infoVideo':
        if (apiLoading.videoGallery) {
          return <option value="">Loading videos...</option>
        }
        if (apiErrors.videoGallery) {
          return <option value="">Error loading videos</option>
        }
        return [
          <option key="default" value="">Choose infoVideo</option>,
          ...apiData.videoGallery.map(item => (
            <option key={item._id} value={item._id} className="truncate">
              {item.title}
            </option>
          ))
        ]

      case 'infoDoc':
        if (apiLoading.infoMarkers) {
          return <option value="">Loading documents...</option>
        }
        if (apiErrors.infoMarkers) {
          return <option value="">Error loading documents</option>
        }
        return [
          <option key="default" value="">Choose infoDoc</option>,
          ...apiData.infoMarkers.map(item => (
            <option key={item._id} value={item._id} className="truncate">
              {item.title}
            </option>
          ))
        ]

      case 'infoImage':
        if (apiLoading.stores) {
          return <option value="">Loading gallery items...</option>
        }
        if (apiErrors.stores) {
          return <option value="">Error loading gallery items</option>
        }
        return [
          <option key="default" value="">Choose gallery item</option>,
          ...apiData.stores.map(item => (
            <option key={item._id} value={item._id} className="truncate">
              {item.title}
            </option>
          ))
        ]

      default:
        return <option value="">Select Content Type</option>
    }
  }, [apiData, apiLoading, apiErrors])

  // Memoize 360s list options to prevent unnecessary re-renders
  const threeSixtyOptions = useMemo(() =>
    _360sList?.map((item, index) => (
      <option key={item._id || index} value={item?.name}>{item?.name}</option>
    )) || [], [_360sList]
  )

  return (
    <div className='inputWrapper w-full h-[calc(100%-100px)]'>
      {/* Offline Notification */}
      <OfflineNotification isOnline={isOnline} />
      <div className='w-full h-fit flex flex-col bg-gray-100 rounded-md shadow p-2 border-1 border-gray-200'>
        <span className='text-sm text-gray-600 font-medium'>Marker title</span>
        <div className='flex items-center h-fit gap-1 p-1 shadow-md rounded mt-2 bg-gray-300'>
          <input
            value={input.name}
            onChange={e=>setInput(prev => ({...prev, name: e.target.value}))}
            className={css}
            type="text"
            placeholder='Enter marker name...'
            maxLength={50}
          />
          <select
            value={input.markerType}
            onChange={e=>setInput(prev => ({...prev, markerType: e.target.value}))}
            className={`${css}`}
          >
            <option value="">Select marker type...</option>
            {markerTypeOptions}
          </select>
          <button
            onClick={handleAddList}
            disabled={!input.name || !input.markerType}
            className={`w-fit px-2 capitalize h-9 rounded text-xs justify-center items-center cursor-pointer shadow transition-colors ${
              !input.name || !input.markerType
                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                : 'bg-gray-800 text-white hover:bg-gray-700'
            }`}
          >
            Add
          </button>
        </div>
      </div>
      <hr className='w-full border-1 border-gray-300 my-2'/>
      <div className='w-full h-full flex-col flex items-center gap-2 bg-gray-50 mt-2 rounded-md shadow p-2 border-1 border-gray-200'>
        <div className='flex flex-col gap-2 w-full overflow-y-auto h-full'>
          {markerList?.map((marker, index) => {
            // Create stable key using marker name and index
            const stableKey = `marker-${marker?.name || `unnamed-${index}`}-${marker?.markerType || 'no-type'}`

            return (
            <div key={stableKey} className='flex w-full h-fit flex-col p-2 bg-gray-200 rounded items-center gap-1'>
              {/* Marker data */}
              <div className='flex w-full h-7 items-center gap-2'>
                <div className='flex w-full items-center gap-6'>
                  <span className='text-xs capitalize text-gray-600'>name:</span>
                  <span className='text-xs capitalize text-gray-600 font-medium'>{marker?.name}</span>
                </div>
                <BiTrash onClick={() => handleDeleteMarker(marker?.name)} className='text-2xl cursor-pointer text-gray-400 border-1 border-gray-400 rounded h-2/3 w-fit'/>
              </div>
              {/* Navigation markers (landingPage, guide, upstairs, downstairs) use _360Name */}
              {(marker?.markerType === 'landingPage' ||
                marker?.markerType === 'guide' ||
                marker?.markerType === 'upstairs' ||
                marker?.markerType === 'downstairs')
                ? <select
                    value={marker?._360Name || ''}
                    onChange={e => handleUpdateMarker(marker?.name, { _360Name: e.target.value })}
                    className={`${css}`}
                  >
                    <option value="">Select 360 Name</option>
                    {threeSixtyOptions}
                  </select>
                : /* Content markers (infoVideo, infoDoc, infoImage) use id field */
                  <select
                    value={marker?.id || ''}
                    onChange={e => handleUpdateMarker(marker?.name, { id: e.target.value })}
                    className={`${css} truncate`}
                  >
                    {getSelectOptions(marker?.markerType)}
                  </select>
              }
            </div>
            )
          })}
        </div>

        {/* Status Message Display */}
        {submitStatus.message && (
          <div className={`w-full p-2 rounded text-xs text-center font-medium ${
            submitStatus.type === 'success'
              ? 'bg-green-100 text-green-800 border border-green-200'
              : submitStatus.type === 'error'
              ? 'bg-red-100 text-red-800 border border-red-200'
              : 'bg-blue-100 text-blue-800 border border-blue-200'
          }`}>
            {submitStatus.message}
          </div>
        )}

        {/* Submit Button with Loading State */}
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className={`flex h-10 w-full max-h-10 px-2 capitalize rounded text-xs justify-center items-center cursor-pointer shadow transition-colors ${
            isSubmitting
              ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
              : 'bg-gray-800 text-white hover:bg-gray-700'
          }`}
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            'Save'
          )}
        </button>
      </div>
    </div>
  )
}

// Export memoized component to prevent unnecessary re-renders
export default memo(MarkersInputList);
