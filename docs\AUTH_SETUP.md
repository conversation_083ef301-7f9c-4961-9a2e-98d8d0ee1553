# Comprehensive Auth.js Authentication System Setup Guide

This guide provides step-by-step instructions for setting up the complete authentication system in your Next.js 14 application.

## 📋 Prerequisites

- Next.js 14+ application
- MongoDB database (local or Atlas)
- Node.js 18+ installed

## 🚀 Installation

### 1. Install Dependencies

```bash
npm install next-auth@beta @auth/mongodb-adapter mongodb bcryptjs resend
```

### 2. Environment Variables

Copy `.env.example` to `.env.local` and configure the following variables:

```env
# Database
MONGODB_URI=mongodb://localhost:27017/lodge-management

# NextAuth.js
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-super-secret-key-here-minimum-32-characters

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Facebook OAuth
FACEBOOK_CLIENT_ID=your-facebook-app-id
FACEBOOK_CLIENT_SECRET=your-facebook-app-secret

# Email (Resend)
RESEND_API_KEY=re_your-resend-api-key
EMAIL_FROM=<EMAIL>

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
```

## 🔧 Provider Setup

### Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set application type to "Web application"
6. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)
7. Copy Client ID and Client Secret to your `.env.local`

### Facebook OAuth Setup

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app or select existing one
3. Add "Facebook Login" product
4. In Facebook Login settings, add Valid OAuth Redirect URIs:
   - `http://localhost:3000/api/auth/callback/facebook` (development)
   - `https://yourdomain.com/api/auth/callback/facebook` (production)
5. Copy App ID and App Secret to your `.env.local`

### Email Provider Setup (Resend)

1. Sign up at [Resend](https://resend.com/)
2. Verify your domain or use their test domain
3. Generate an API key
4. Add the API key to your `.env.local`

## 🗄️ Database Setup

### MongoDB Connection

The system uses MongoDB with Mongoose for data modeling. Ensure your MongoDB instance is running and accessible.

### User Model

The User model includes:
- Basic profile information (name, email, image)
- Role-based access control (guest, user, manager, admin)
- Authentication tracking (login count, last login)
- Guest user support for bookings/purchases

## 🔐 Security Features

### Rate Limiting

The middleware implements rate limiting:
- Standard routes: 100 requests per 15 minutes
- Auth routes: 20 requests per 15 minutes
- IP-based tracking with automatic cleanup

### CSRF Protection

Built-in CSRF protection through Auth.js with:
- Secure session cookies
- CSRF token validation
- Same-site cookie policy

### Password Security

- Bcrypt hashing with salt rounds
- Password strength validation
- Secure password reset flow

### Security Headers

Automatic security headers:
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Content Security Policy (production)
- Referrer Policy: strict-origin-when-cross-origin

## 🎯 Role-Based Access Control

### Role Hierarchy

1. **Guest** (0) - Unauthenticated users
2. **User** (1) - Authenticated users
3. **Manager** (2) - Can manage lodges and bookings
4. **Admin** (3) - Full system access

### Automatic Admin Assignment

Users with email `<EMAIL>` are automatically assigned admin role.

### Permission Checking

```javascript
import { getCurrentUser, hasRole, isAdmin } from '@/lib/auth-utils';

// Check current user
const user = await getCurrentUser();

// Check specific role
const canManage = await hasRole('manager');

// Check admin status
const isAdminUser = await isAdmin();
```

## 🛡️ API Protection

### Middleware Usage

```javascript
import { requireAuth, requireRole } from '@/lib/middleware';

// Require authentication
export async function GET(request) {
  const authCheck = await requireAuth(request);
  if (authCheck) return authCheck;
  
  // Your protected code here
}

// Require specific role
export async function POST(request) {
  const roleCheck = await requireRole('manager')(request);
  if (roleCheck) return roleCheck;
  
  // Your protected code here
}
```

## 👥 Guest User Support

### Guest Bookings

Unauthenticated users can:
- Browse lodges
- Make bookings with contact information
- Automatically get user accounts created
- Optionally set passwords during booking

### Guest Purchases

Unauthenticated users can:
- Browse gallery
- Add items to cart
- Complete purchases with billing information
- Automatically get user accounts created
- Optionally set passwords during checkout

## 🔄 Session Management

### Database Sessions

- Sessions stored in MongoDB
- 30-day session lifetime
- 24-hour update frequency
- Automatic cleanup of expired sessions

### Session Data

Sessions include:
- User ID and basic profile
- Role information
- Login tracking
- Profile preferences

## 📧 Email Authentication

### Magic Link Flow

1. User enters email address
2. System sends secure sign-in link
3. User clicks link to authenticate
4. Automatic account creation if needed
5. Redirect to intended destination

### Email Templates

Customizable email templates for:
- Magic link authentication
- Welcome messages
- Password reset (if implemented)
- Account verification

## 🚨 Error Handling

### Custom Error Pages

- `/auth/error` - Authentication errors
- `/auth/verify-request` - Email verification pending
- Detailed error messages for debugging
- User-friendly error descriptions

### Error Types

- Configuration errors
- Access denied
- Invalid credentials
- OAuth callback errors
- Email delivery issues

## 📊 Monitoring & Logging

### Authentication Events

Logged events include:
- Sign in/out events
- Account creation
- Role changes
- Failed authentication attempts
- Rate limit violations

### Security Monitoring

- Failed login tracking
- Suspicious activity detection
- Rate limit monitoring
- Session anomaly detection

## 🧪 Testing

### Development Testing

```bash
# Start development server
npm run dev

# Test authentication flows
# 1. Visit http://localhost:3000/auth/signin
# 2. Test each provider (Google, Facebook, Email, Credentials)
# 3. Verify role assignment
# 4. Test guest flows
```

### Production Checklist

- [ ] All environment variables configured
- [ ] OAuth providers configured with production URLs
- [ ] Email provider configured and tested
- [ ] Database connection secure
- [ ] HTTPS enabled
- [ ] Security headers verified
- [ ] Rate limiting tested
- [ ] Error pages customized
- [ ] Monitoring configured

## 🔧 Troubleshooting

### Common Issues

1. **OAuth Redirect Mismatch**
   - Verify redirect URIs in provider settings
   - Check NEXTAUTH_URL environment variable

2. **Email Not Sending**
   - Verify email provider API key
   - Check email domain verification
   - Review email provider logs

3. **Database Connection Issues**
   - Verify MongoDB URI format
   - Check network connectivity
   - Ensure database exists

4. **Session Issues**
   - Clear browser cookies
   - Check NEXTAUTH_SECRET configuration
   - Verify database adapter setup

### Debug Mode

Enable debug logging in development:

```env
NEXTAUTH_DEBUG=true
```

## 📚 Additional Resources

- [Auth.js Documentation](https://authjs.dev/)
- [MongoDB Adapter](https://authjs.dev/reference/adapter/mongodb)
- [Provider Configuration](https://authjs.dev/reference/core/providers)
- [Security Best Practices](https://authjs.dev/guides/basics/securing-your-site)

## 🆘 Support

For issues or questions:
1. Check the troubleshooting section
2. Review Auth.js documentation
3. Check GitHub issues
4. Contact development team
