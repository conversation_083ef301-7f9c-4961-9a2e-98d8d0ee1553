'use client';

import { useMemo } from 'react';

export default function ClientStats({ clients }) {
  const stats = useMemo(() => {
    if (!clients || clients.length === 0) {
      return {
        total: 0,
        active: 0,
        guests: 0,
        totalRevenue: 0,
        averageBookings: 0,
        topSpenders: [],
        recentSignups: 0,
        byRole: {},
        byLocation: {},
      };
    }

    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const calculations = {
      total: clients.length,
      active: clients.filter(c => c.isActive && !c.isBlocked).length,
      guests: clients.filter(c => c.isGuest).length,
      totalRevenue: 0,
      totalBookings: 0,
      recentSignups: clients.filter(c => new Date(c.createdAt) >= thirtyDaysAgo).length,
      byRole: {},
      byLocation: {},
    };

    // Calculate revenue and booking stats
    clients.forEach(client => {
      if (client.stats?.bookings) {
        calculations.totalRevenue += client.stats.bookings.totalSpent || 0;
        calculations.totalBookings += client.stats.bookings.totalBookings || 0;
      }

      // Count by role
      calculations.byRole[client.role] = (calculations.byRole[client.role] || 0) + 1;

      // Count by location (country)
      const country = client.address?.country || 'Unknown';
      calculations.byLocation[country] = (calculations.byLocation[country] || 0) + 1;
    });

    // Calculate averages
    calculations.averageBookings = calculations.total > 0 
      ? calculations.totalBookings / calculations.total 
      : 0;

    calculations.averageRevenue = calculations.total > 0 
      ? calculations.totalRevenue / calculations.total 
      : 0;

    // Get top spenders
    calculations.topSpenders = clients
      .filter(c => c.stats?.bookings?.totalSpent > 0)
      .sort((a, b) => (b.stats.bookings.totalSpent || 0) - (a.stats.bookings.totalSpent || 0))
      .slice(0, 5)
      .map(c => ({
        id: c._id,
        name: c.name,
        email: c.email,
        totalSpent: c.stats.bookings.totalSpent || 0,
        totalBookings: c.stats.bookings.totalBookings || 0,
      }));

    return calculations;
  }, [clients]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Clients */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Total Clients</dt>
              <dd className="text-lg font-medium text-gray-900">{formatNumber(stats.total)}</dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-green-600 font-medium">+{stats.recentSignups}</span>
            <span className="text-gray-500 ml-1">new this month</span>
          </div>
        </div>
      </div>

      {/* Active Clients */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Active Clients</dt>
              <dd className="text-lg font-medium text-gray-900">{formatNumber(stats.active)}</dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-gray-600">
              {stats.total > 0 ? ((stats.active / stats.total) * 100).toFixed(1) : 0}% active rate
            </span>
          </div>
        </div>
      </div>

      {/* Total Revenue */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
              <dd className="text-lg font-medium text-gray-900">{formatCurrency(stats.totalRevenue)}</dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-gray-600">
              {formatCurrency(stats.averageRevenue)} avg per client
            </span>
          </div>
        </div>
      </div>

      {/* Average Bookings */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Avg Bookings</dt>
              <dd className="text-lg font-medium text-gray-900">{stats.averageBookings.toFixed(1)}</dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-gray-600">
              {formatNumber(stats.totalBookings)} total bookings
            </span>
          </div>
        </div>
      </div>

      {/* Role Distribution */}
      <div className="bg-white shadow rounded-lg p-6 md:col-span-2">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Client Distribution</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">By Role</h4>
            <div className="space-y-2">
              {Object.entries(stats.byRole).map(([role, count]) => (
                <div key={role} className="flex justify-between text-sm">
                  <span className="capitalize text-gray-600">{role}</span>
                  <span className="font-medium">{count}</span>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Top Locations</h4>
            <div className="space-y-2">
              {Object.entries(stats.byLocation)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 4)
                .map(([location, count]) => (
                  <div key={location} className="flex justify-between text-sm">
                    <span className="text-gray-600">{location}</span>
                    <span className="font-medium">{count}</span>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>

      {/* Top Spenders */}
      <div className="bg-white shadow rounded-lg p-6 md:col-span-2">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Top Spenders</h3>
        {stats.topSpenders.length > 0 ? (
          <div className="space-y-3">
            {stats.topSpenders.map((client, index) => (
              <div key={client.id} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3">
                    {index + 1}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{client.name}</div>
                    <div className="text-xs text-gray-500">{client.email}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {formatCurrency(client.totalSpent)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {client.totalBookings} bookings
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-4">
            No client spending data available
          </div>
        )}
      </div>
    </div>
  );
}
