#!/usr/bin/env node

/**
 * Payment System Testing Script
 * 
 * This script tests the complete payment flow to identify issues
 */

const https = require('https');
const http = require('http');

// Configuration
const isDevelopment = process.env.NODE_ENV !== 'production';
const baseUrl = isDevelopment ? 'https://localhost:3001' : 'https://victorchelemu.com';
const protocol = https; // Always use HTTPS

console.log(`🧪 Testing Payment System on ${baseUrl}`);
console.log('=' .repeat(60));

// Test endpoints
const endpoints = [
  '/api/payments/create-intent',
  '/api/payments/webhook',
  '/api/bookings',
];

async function testEndpoint(endpoint, method = 'GET', body = null) {
  return new Promise((resolve) => {
    const url = new URL(endpoint, baseUrl);
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isDevelopment ? 3001 : 443),
      path: url.pathname,
      method: method,
      headers: {
        'User-Agent': 'Payment-Test-Script/1.0',
        'Content-Type': 'application/json',
      },
      // For development HTTPS with self-signed certificates
      rejectUnauthorized: !isDevelopment
    };

    if (body) {
      const bodyString = JSON.stringify(body);
      options.headers['Content-Length'] = Buffer.byteLength(bodyString);
    }

    const req = protocol.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = data ? JSON.parse(data) : {};
          resolve({
            endpoint,
            method,
            status: res.statusCode,
            statusText: res.statusMessage,
            headers: res.headers,
            data: parsedData,
            success: res.statusCode >= 200 && res.statusCode < 300
          });
        } catch (parseError) {
          resolve({
            endpoint,
            method,
            status: res.statusCode,
            statusText: res.statusMessage,
            headers: res.headers,
            data: data,
            parseError: parseError.message,
            success: false
          });
        }
      });
    });

    req.on('error', (error) => {
      resolve({
        endpoint,
        method,
        status: 'ERROR',
        statusText: error.message,
        success: false
      });
    });

    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        endpoint,
        method,
        status: 'TIMEOUT',
        statusText: 'Request timeout',
        success: false
      });
    });

    if (body) {
      req.write(JSON.stringify(body));
    }

    req.end();
  });
}

async function testStripeConfiguration() {
  console.log('\n🔧 Testing Stripe Configuration...');
  console.log('-'.repeat(40));
  
  // Check environment variables
  const stripePublishable = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
  const stripeSecret = process.env.STRIPE_SECRET_KEY;
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
  
  console.log('Stripe Publishable Key:', stripePublishable ? '✅ Set' : '❌ Missing');
  console.log('Stripe Secret Key:', stripeSecret ? '✅ Set' : '❌ Missing');
  console.log('Webhook Secret:', webhookSecret ? '✅ Set' : '❌ Missing');
  
  if (stripePublishable) {
    const keyType = stripePublishable.startsWith('pk_test_') ? 'Test' : 
                   stripePublishable.startsWith('pk_live_') ? 'Live' : 'Unknown';
    console.log('Key Type:', keyType);
  }
  
  return {
    publishableKey: !!stripePublishable,
    secretKey: !!stripeSecret,
    webhookSecret: !!webhookSecret,
    allConfigured: !!(stripePublishable && stripeSecret && webhookSecret)
  };
}

async function testPaymentFlow() {
  console.log('\n💳 Testing Payment Flow...');
  console.log('-'.repeat(40));
  
  // First, we need a booking to test with
  console.log('1. Testing booking creation...');
  
  // Test payment intent creation (this will fail without a real booking)
  console.log('2. Testing payment intent creation...');
  const paymentIntentTest = await testEndpoint('/api/payments/create-intent', 'POST', {
    bookingId: 'test-booking-id',
    amount: 100.00
  });
  
  console.log('Payment Intent Test:', paymentIntentTest.success ? '✅ PASS' : '❌ FAIL');
  if (!paymentIntentTest.success) {
    console.log('Error:', paymentIntentTest.data?.message || paymentIntentTest.statusText);
  }
  
  return {
    paymentIntentCreation: paymentIntentTest.success
  };
}

async function testWebhookEndpoint() {
  console.log('\n🔗 Testing Webhook Endpoint...');
  console.log('-'.repeat(40));
  
  // Test webhook endpoint accessibility
  const webhookTest = await testEndpoint('/api/payments/webhook', 'POST', {
    type: 'test',
    data: { object: { id: 'test' } }
  });
  
  console.log('Webhook Endpoint:', webhookTest.status === 400 ? '✅ ACCESSIBLE' : '❌ ISSUE');
  console.log('Status:', webhookTest.status, webhookTest.statusText);
  
  return {
    webhookAccessible: webhookTest.status === 400 // 400 is expected for invalid signature
  };
}

async function runAllTests() {
  console.log('🚀 Starting Payment System Tests...\n');
  
  const results = {
    stripeConfig: await testStripeConfiguration(),
    paymentFlow: await testPaymentFlow(),
    webhook: await testWebhookEndpoint()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('=' .repeat(60));
  
  // Stripe Configuration
  console.log('Stripe Configuration:');
  console.log(`  Publishable Key: ${results.stripeConfig.publishableKey ? '✅' : '❌'}`);
  console.log(`  Secret Key: ${results.stripeConfig.secretKey ? '✅' : '❌'}`);
  console.log(`  Webhook Secret: ${results.stripeConfig.webhookSecret ? '✅' : '❌'}`);
  console.log(`  Overall: ${results.stripeConfig.allConfigured ? '✅ CONFIGURED' : '❌ INCOMPLETE'}`);
  
  // Payment Flow
  console.log('\nPayment Flow:');
  console.log(`  Payment Intent Creation: ${results.paymentFlow.paymentIntentCreation ? '✅' : '❌'}`);
  
  // Webhook
  console.log('\nWebhook System:');
  console.log(`  Webhook Endpoint: ${results.webhook.webhookAccessible ? '✅' : '❌'}`);
  
  // Overall Assessment
  const allPassed = results.stripeConfig.allConfigured && 
                   results.webhook.webhookAccessible;
  
  console.log('\n🎯 Overall Assessment:');
  if (allPassed) {
    console.log('✅ Payment system appears to be configured correctly!');
    console.log('✅ Ready for testing with real bookings');
  } else {
    console.log('❌ Payment system has configuration issues');
    console.log('❌ Review the failed tests above');
  }
  
  // Recommendations
  console.log('\n💡 Recommendations:');
  if (!results.stripeConfig.allConfigured) {
    console.log('- Complete Stripe environment variable configuration');
  }
  if (!results.webhook.webhookAccessible) {
    console.log('- Check webhook endpoint configuration');
  }
  console.log('- Test with a real booking in development');
  console.log('- Verify webhook URL in Stripe dashboard');
  console.log('- Test payment flow end-to-end');
  
  process.exit(allPassed ? 0 : 1);
}

// Handle script execution
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('❌ Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testEndpoint, testStripeConfiguration, testPaymentFlow, runAllTests };
